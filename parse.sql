drop table if exists "SpiderResume";
create table "SpiderResume"
(
    "resumeId"            serial8 primary key,
    "confirmState"        smallint not null default 2,
    "fileName"            text     not null,
    "userName"            text,
    birthday              date,
    sex                   text,
    mobile                text,
    email                 text,
    address               text,
    education             text,
    "educationExperience" text,
    "workExperience"      text,
    "projectExperience"   text,
    skill                 text,
    intro                 text,
    award                 text,
    certificate           text,
    "jobStatus"           text,
    "expectSalary"        text,
    "expectCity"          text,
    "expectJob"           text,
    "workYear"            text,
    "resumeOwner"         bigint   not null,
    "resumeSource"        smallint not null,
    "enableSync"          smallint not null default 2,
    "createTime"          timestamp(0)      default now() not null,
    "updateTime"          timestamp(0)      default now() not null
);
comment on table "SpiderResume" is '爬虫简历';
comment on column "SpiderResume"."resumeId" is '简历ID';
comment on column "SpiderResume"."confirmState" is '简历确认状态 1-已确认 2-未确认';
comment on column "SpiderResume"."fileName" is '文件名';
comment on column "SpiderResume"."userName" is '姓名';
comment on column "SpiderResume"."birthday" is '出生日期';
comment on column "SpiderResume"."sex" is '性别';
comment on column "SpiderResume"."mobile" is '手机号';
comment on column "SpiderResume"."email" is '邮箱';
comment on column "SpiderResume"."address" is '地址';
comment on column "SpiderResume"."education" is '学历';
comment on column "SpiderResume"."educationExperience" is '教育经历';
comment on column "SpiderResume"."workExperience" is '工作经历';
comment on column "SpiderResume"."projectExperience" is '项目经历';
comment on column "SpiderResume"."skill" is '技能';
comment on column "SpiderResume"."intro" is '个人介绍';
comment on column "SpiderResume"."award" is '获奖情况';
comment on column "SpiderResume"."certificate" is '证书';
comment on column "SpiderResume"."jobStatus" is '工作状态';
comment on column "SpiderResume"."expectSalary" is '期望薪资';
comment on column "SpiderResume"."expectCity" is '期望城市';
comment on column "SpiderResume"."expectJob" is '期望职位';
comment on column "SpiderResume"."workYear" is '工作年限';
comment on column "SpiderResume"."resumeOwner" is '简历拥有者';
comment on column "SpiderResume"."resumeSource" is '渠道来源';
comment on column "SpiderResume"."enableSync" is '是否同步 1是 2否';
comment on column "SpiderResume"."createTime" is '创建时间';
comment on column "SpiderResume"."updateTime" is '更新时间';

insert into "SysDic"("tableName", "propertyName", "propertyCode", "simpleName", "defaultName", "fullName", "createBy",
                     "updateBy")
values ('resumeSource', 'resumeSource', 1, '58同城', '58同城', '58同城', 1, 1),
       ('resumeSource', 'resumeSource', 2, 'BOSS直聘', 'BOSS直聘', 'BOSS直聘', 1, 1),
       ('resumeSource', 'resumeSource', 3, '智联招聘', '智联招聘', '智联招聘', 1, 1);

INSERT INTO public."SpiderResume" ("confirmState", "fileName", "userName", "birthday", sex, mobile, email, address,
                                   education, "educationExperience", "workExperience", "projectExperience", skill,
                                   intro, award, certificate, "jobStatus", "expectSalary", "expectCity", "expectJob",
                                   "workYear", "resumeOwner", "resumeSource", "createTime", "updateTime")
VALUES (2, 'test.txt', '林成华', now()-interval '37year', '男', '13800000000', '<EMAIL>', '烟台', '本科',
        '教育经历\n烟台工程职业技术学院 大专',
        '工作经历\n龙口市南山双语学校初中部  英语老师项目经历\n在校期间参加过行政商务培训,懂得基本接待礼仪,普通话二级甲等。担任行政专员期间对接各部门人员,沟通业务信息,\n协助业务高效开展。及时完成各资料分类,',
        '项目经历',
        '技能证书\n大学英语四、六级证书(CET-4/6),良好的听说读写能力;\n全国计算机二级证书,熟练运用office相关软件;\n普通话二级甲等证书。',
        '个人优势\n性格比较冷静,为人认真负责诚实,,学习礼仪形体等课程,身高形象也算还行',
        '获奖情况\n2022年11月获校级三等学业奖学金\n2023年5月获院级五四优秀团员\n2023年9月获鲁东大学全日制专业学位研究生专业技能比赛一等奖\n2023年11月获校级二等学业奖学金校园经历',
        '\n高级保育员\n中级育婴师\n大学生英语六级463分通过\n普通话90.2分二级甲等\n歌唱(民族唱法)社会艺术水平等级证书六级\n漫画社会艺术水平等级证书七级\n教师资格证(幼儿园)\n优秀毕业生',
        '1', '面议', '烟台 福山区 福海路', '其他', '10以上', 1, 1, '2025-02-13 03:27:39', '2025-02-13 03:27:39');
