来自钉钉专属商务邮箱
------------------------------------------------------------------
发件人：58同城简历<jian<PERSON>@zp.58.com>
日　期：2023年07月27日 20:39:46
收件人：<<EMAIL>>
主　题：(58.com)应聘贵公司长白班管住包装-青岛 即墨 即墨周边-张旭峰
 <http://www.58.com/ > 应聘职位：长白班管住包装 <http://qd.58.com/partime/52301517187994x.shtml >。请通过简历中的电话或邮件联系我（请勿直接回复邮件）。 更新时间：2023-07-24
张旭峰（男，21岁）

 *

 *
社会人才

 *
中专/技校

 *
无社会实践经验

 *
籍贯

 *
现居住

 *
${sparetime}$
 电子邮箱：
查看简历详情 <https://zpjl.58.com//delivery/emailProtocol/resume/detail/n9xaIU1-HsLTMndQ1B_179ftBwub_GZhUj3xA9eE5F-uZWNmKA_VU2eVafepx0oLTZDC8waTsmKDJP1dY96Cpg== >
求职意向

 *

 *
期望职位：其他

 *
期望薪资：面议

 *
期望地区：青岛 即墨 即墨周边
扫码下载招才猫直聘，招聘更高效！
同时查看58同城+赶集网双平台简历
招人效率提升77.3%
该简历来自 58同城(www.58.com)
<div class="__aliyun_email_body_block"><div  style="font-family: Tahoma, Arial, STHeitiSC-Light, SimSun"><div  style="clear: both;"><span  style="font-family: Tahoma, Arial, STHeitiSC-Light, SimSun;"><br ></span></div><div  style="margin: 0px 0px 0px 30px; font-family: Tahoma, Arial, STHeiti, SimSun; font-size: 14px; color: rgb(0, 0, 0);"><div  class=" __aliyun_node_has_color" style="color: rgb(0, 0, 0); font-size: 14px; font-family: Tahoma, Arial, STHeitiSC-Light, SimSun;"><div  style="margin: 14px 0px;"><br ><br ><div  class=" __aliyun_node_has_color" style="font-family: PingFangSC-Regular; font-size: 14px; color: rgb(102, 102, 102);">来自钉钉专属商务邮箱</div>------------------------------------------------------------------<br >发件人：58同城简历&lt;<EMAIL>&gt;<br >日　期：2023年07月27日 20:39:46<br >收件人：&lt;<EMAIL>&gt;<br >主　题：(58.com)应聘贵公司长白班管住包装-青岛 即墨 即墨周边-张旭峰<br ><br >




  <div  class=" __aliyun_node_has_bgimage" style="background: url('http://pic2.58.com/ui7/job/resume/resume_prebg.gif') repeat; font-size: 12px; font-family: arial, 宋体;">
   <div >
    <img  src="https://wos2.58cdn.com.cn/iJkFeDcBiJiJ/rocket/1ZjPlEHP3o.png" style="width:907.0px;margin:.0px auto;vertical-align:bottom;">
   </div>
   <div  class=" __aliyun_node_has_color" style="width: 907px; margin: 0px auto; background: none; color: rgb(153, 153, 153); height: 50px; overflow: hidden; position: relative;">
    <a  href="http://www.58.com/" target="_blank" style="margin-left: 4px; float: left;"><img  alt="中文最大生活信息门户" src="http://pic1.58cdn.com.cn/nowater/cxnomark/n_v2fe5411dfa0d74b8489181dd486dfd552.png" style="border:.0px;margin-top:12.0px;vertical-align:bottom;"></a>
    <span  class=" __aliyun_node_has_color" style="float: right; color: rgb(64, 64, 64); margin-top: 30px;"><b >应聘职位</b>：<a  class=" __aliyun_node_has_color" href="http://qd.58.com/partime/52301517187994x.shtml" target="_blank" style="color: rgb(34, 85, 221); text-decoration: underline;">长白班管住包装</a>。请通过简历中的电话或邮件联系我（请勿直接回复邮件）。 更新时间：2023-07-24</span><div  style="clear: both"></div><div  style="clear: both;"></div>
   </div>
   <div  style="width: 907px; margin: 0px auto;">
    <div  style="padding: 0px; background: none;">
     <div  style="padding: 0px;">
      <div  class=" __aliyun_node_has_bgimage" style="background: url('http://pic2.58.com/ui7/job/resume/rpbgt.gif') no-repeat; padding: 0px 30px; height: 10px; overflow: hidden; zoom: 1;"></div>
      <div  style="clear: both;"><br ></div><div  class=" __aliyun_node_has_bgimage" style="background: url('http://pic2.58.com/ui7/job/resume/rpbgc.gif') repeat-y; padding: 7px 30px;">
       <div  style="overflow: hidden; zoom: 1;">
        <h3  style="margin: 0px; padding: 0px 0px 10px; font-size: 22px; line-height: 22px; font-family: 微软雅黑; font-weight: 400;">张旭峰<span  class=" __aliyun_node_has_color" style="font-size: 16px; color: rgb(52, 52, 52); font-family: arial, 宋体; padding: 0px 10px;">（男，21岁）</span></h3>
        <div  style="padding: 11px 0px 0px; border: none;">
         <ul  style="margin: 0px; padding: 0px; list-style: none; width: 680px; float: left; overflow: hidden; zoom: 1;"><li ><div  style="clear: both;">
          </div></li><li  style="padding: 0px 10px 0px 0px; list-style: none; float: left; display: inline; margin: 5px 10px 5px 0px; border-right: 1px solid rgb(234, 234, 234); line-height: 16px; font-size: 14px; white-space: nowrap;"><div  style="clear: both; font-size: 14px; line-height: 16px;">社会人才
          </div></li><li  style="padding: 0px 10px 0px 0px; list-style: none; float: left; display: inline; margin: 5px 10px 5px 0px; border-right: 1px solid rgb(234, 234, 234); line-height: 16px; font-size: 14px; white-space: nowrap;"><div  style="clear: both; font-size: 14px; line-height: 16px;">中专/技校
          </div></li><li  style="padding: 0px 10px 0px 0px; list-style: none; float: left; display: inline; margin: 5px 10px 5px 0px; border-right: 1px solid rgb(234, 234, 234); line-height: 16px; font-size: 14px; white-space: nowrap;"><div  style="clear: both; font-size: 14px; line-height: 16px;">无社会实践经验
          </div></li><li  style="padding: 0px 10px 0px 0px; list-style: none; float: left; display: inline; margin: 5px 10px 5px 0px; border-right: 1px solid rgb(234, 234, 234); line-height: 16px; font-size: 14px; white-space: nowrap;"><div  style="clear: both; font-size: 14px; line-height: 16px;">籍贯
          </div></li><li  style="padding: 0px 10px 0px 0px; list-style: none; float: left; display: inline; margin: 5px 10px 5px 0px; line-height: 16px; font-size: 14px; white-space: nowrap;"><div  style="clear: both; font-size: 14px; line-height: 16px;">现居住
          </div></li><li  style="padding: 0px 10px 0px 0px; list-style: none; float: left; display: inline; margin: 5px 10px 5px 0px; line-height: 16px; font-size: 14px; white-space: nowrap;"><div  style="clear: both; font-size: 14px; line-height: 16px;">${sparetime}$<div  style="clear: both;"></div>
         </div><div  style="clear: both"></div></li></ul><div  style="clear: both"></div><div  style="clear: both;"></div>
        </div>
       </div>
       <div  style="margin: 0px 0px 20px; padding-right: 152px; position: relative;">
        <div  class="infoview __aliyun_node_has_color" style="font-size: 12px; color: rgb(64, 64, 64); overflow: hidden; zoom: 1; padding: 11px 0px 0px; border: none;">

        </div>
        <div  style="position: relative; overflow: hidden; zoom: 1; padding: 10px 0px 0px;">
         <div >
          <div  style="border: none;">
           <p  style="margin: 0px; padding: 0px; font-size: 14px; line-height: 40px; vertical-align: middle;"> <img  alt="网络异常，请检查网络并重新打开邮件" src="https://zpjl.58.com/phone/emailProtocol/n9xaIU1-HsLTMndQ1B_179ftBwub_GZhUj3xA9eE5F_TNpcZNt0vdt43U2FoJ5cDY6wgu7CY3zXf_bl5UdHyDo3gBMWXgBhe8z2fM42XIAudeF1B54sUysho5LsqAE9eARR75aMEVSkuoEZ_f4cwzHd9SZzTLj6QYYFALXJSJ3Y=.png" style="padding-right:8.0px;width:210.0px;vertical-align:middle;"> 电子邮箱：<span  class=" __aliyun_node_has_color" style="color: rgb(224, 89, 71); font-size: 20px;"></span> </p>
          </div>
         </div>
        </div>
        <a  class=" __aliyun_node_has_color __aliyun_node_has_bgcolor" href="https://zpjl.58.com//delivery/emailProtocol/resume/detail/n9xaIU1-HsLTMndQ1B_179ftBwub_GZhUj3xA9eE5F-uZWNmKA_VU2eVafepx0oLTZDC8waTsmKDJP1dY96Cpg==" target="_blank" style="right: 20px; top: 18px; background: rgb(216, 216, 216); font-size: 15px; padding: 5px 15px; color: rgb(79, 81, 83); border: 1px solid rgb(167, 167, 167); text-decoration: none;">查看简历详情</a>
       </div>
       <div  class="margin:15px 0 26px;">
        <div  style="overflow: hidden; zoom: 1; border-top: 1px solid rgb(228, 228, 228);">
         <h3  class=" __aliyun_node_has_color" style="margin: 0px; font-size: 16px; line-height: 18px; font-weight: 700; color: rgb(64, 64, 64); padding: 26px 0px 0px;">求职意向</h3>
        </div>
        <div  class=" __aliyun_node_has_color" style="overflow: hidden; font-size: 12px; color: rgb(64, 64, 64); padding: 11px 0px 0px; border: none;">
         <ul  style="margin: 0px; padding: 0px; list-style: none; overflow: hidden; zoom: 1;"><li ><div  style="clear: both;">
          </div></li><li  style="padding: 0px; list-style: none; line-height: 22px; font-size: 14px; margin: 5px 10px 5px 0px; white-space: nowrap;"><div  style="clear: both; font-size: 14px; line-height: 22px;"><span  class="c66 __aliyun_node_has_color" style="color: rgb(64, 64, 64);">期望职位：</span>其他
          </div></li><li  style="padding: 0px; list-style: none; line-height: 22px; font-size: 14px; margin: 5px 10px 5px 0px; white-space: nowrap;"><div  style="clear: both; font-size: 14px; line-height: 22px;"><span  class="c66 __aliyun_node_has_color" style="color: rgb(64, 64, 64);">期望薪资：</span>面议
          </div></li><li  style="padding: 0px; list-style: none; line-height: 22px; font-size: 14px; margin: 5px 10px 5px 0px; white-space: nowrap;"><div  style="clear: both; font-size: 14px; line-height: 22px;"><span  class="c66 __aliyun_node_has_color" style="color: rgb(64, 64, 64);">期望地区：</span>青岛 即墨 即墨周边
         </div></li></ul>
        </div>
       </div>
       <p  style="margin: 0px; padding: 0px; text-align: center;"><img  border="0" height="0" src="https://jianli.58.com/resumefeedback/eusermsg/?id=12224534039236356" width="0" style="vertical-align:bottom;"></p>
      </div><div  style="clear: both;"><br ></div>
      <div  class=" __aliyun_node_has_bgimage" style="background: url('http://pic2.58.com/ui7/job/resume/rpbgb.gif') no-repeat; padding: 0px 30px; height: 10px; overflow: hidden; zoom: 1;"></div>
     </div>
     <div  style="font-size: 0px; clear: both; overflow: hidden;"></div>
    </div>
   </div>
   <div  class=" __aliyun_node_has_bgcolor" style="background: rgb(255, 255, 255); padding: 10px 25px; width: 850px; margin: 0px auto; border-radius: 3px; box-shadow: rgb(204, 204, 204) 0px 5px 10px;">
    <img  src="http://pic1.58cdn.com.cn/nowater/fangfe/n_v204b03cdcbb5942b0938ef0f32c553f7c.png" style="width:100.0px;vertical-align:top;">
    <div  style="display: inline-block; margin-left: 20px;">
     <p  class=" __aliyun_node_has_color" style="font-size: 18px; font-weight: 700; margin: 10px 0px; color: rgb(51, 51, 51);">扫码下载招才猫直聘，招聘更高效！</p>
     <p  class=" __aliyun_node_has_color" style="font-size: 14px; margin: 5px 0px; color: rgb(102, 102, 102);">同时查看58同城+赶集网双平台简历</p>
     <p  class=" __aliyun_node_has_color" style="font-size: 14px; margin: 0px; color: rgb(102, 102, 102);">招人效率提升77.3%</p>
    </div>
   </div>
   <div  class=" __aliyun_node_has_color" style="width: 907px; margin: 0px auto; background: none; color: rgb(153, 153, 153);">
    <p  style="margin: 0px; text-align: center; line-height: 24px; padding: 10px 0px; font-size: 12px;">该简历来自 58同城(www.58.com)</p>
   </div>
  </div>
  <img  height="1" src="http://linktrace.easeyedelivery.com/MailLink/LogoImageHandler.jpg?EASEYEUID=N132-GIYTQNZZ-GIZTANZSG4ZDAMZZGQ3DONBUGU3TCMA-JY-55AD4715&amp;" width="1" style="vertical-align:bottom;">

<br ></div><div  style="line-height: 20px; clear: both;"><br ></div></div></div><div  style="line-height: 20px; clear: both;"><br ></div></div></div>