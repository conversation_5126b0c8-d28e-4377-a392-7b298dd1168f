{
  "简历": {
    "个人信息": {
      "姓名": "张三",
      "性别": "男",
      "年龄": "28岁",
      "邮箱": "<EMAIL>",
      "学历": "本科",
      "求职意向": "大数据开发工程师"
    },
    "自我介绍": "具有3年以上的离线数据仓库搭建经验，熟悉Hadoop、Flink等大数据技术栈。具备优秀的脚本编写能力及数据处理能力。",
    "工作经历": [
      {
        "公司名称": "惠买科技",
        "职位": "大数据开发工程师",
        "时间范围": "2020.06-2020.12",
        "职责描述": [
          "参与数据仓库的分层建模与搭建",
          "负责DWD层宽表和拉链表的搭建",
          "设计公司指标，包括周回流数据、流失用户等",
          "编写脚本并进行测试"
        ],
        "技术描述": [
          "使用HDFS、Hive、Spark、Sqoop等工具进行数据处理",
          "利用系统函数解析日志数据，生成五张日志表",
          "基于DWD层建立主题宽表，并进行聚合分析",
          "使用Kylin进行即席查询，使用Atlas管理元数据"
        ]
      }
    ],
    "教育经历": [
      {
        "学校名称": "某大学",
        "专业": "计算机科学与技术",
        "学历": "本科",
        "时间范围": "2016-2020"
      }
    ],
    "项目经验": [
      {
        "项目名称": "惠买离线数据仓库",
        "职责描述": [
          "参与数据仓库的分层建模与搭建",
          "负责DWD层宽表和拉链表的搭建",
          "设计公司指标，包括周回流数据、流失用户等"
        ],
        "技术栈": [
          "Hadoop",
          "Hive",
          "Spark",
          "Sqoop",
          "MySQL",
          "Azkaban",
          "Kylin",
          "Zabbix",
          "Atlas"
        ]
      }
    ],
    "专业技能": [
      "熟悉Hadoop、Flink等大数据技术栈",
      "具备优秀的脚本编写能力及数据处理能力",
      "熟悉数据仓库分层建模与搭建",
      "熟练使用Hive、Spark、Sqoop进行数据处理"
    ]
  }