package com.jc.parse.service;

import java.lang.reflect.Array;
import java.util.Arrays;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * date : 2025/3/24
 * description :
 *
 * <AUTHOR> zhencai.cheng
 */
public class Ts {

    public static void main(String[] args) {
        String content = "教育经历\n" +
                "1ffbf247d99883ba1nx_2d65ElVVwYu8UfydWOKqnfHRPxFi3g~~\n" +
                "1ffb\n" +
                "f247d99883ba1nx_\n" +
                "2d65ElVVwYu8Ufyd\n" +
                "W\n" +
                "OKqnfH\n" +
                "RPxF\n" +
                "i3g~~\n" +
                "1ffb\n" +
                "f247d99883ba1nx_\n" +
                "2d65ElVVwYu8Ufyd\n" +
                "W\n" +
                "OKqnfH\n" +
                "RPxF\n" +
                "i3g~~\n" +
                "1ffb\n" +
                "f247d99883ba1nx_\n" +
                "2d65ElVVwYu8Ufyd\n" +
                "W\n" +
                "OKqnfH\n" +
                "RPxF\n" +
                "i3g~~\n" +
                "1ffb\n" +
                "f247d99883ba1nx_\n" +
                "2d65ElVVwYu8Ufyd\n" +
                "W\n" +
                "OKqnfH\n" +
                "RPxF\n" +
                "i3g~~\n" +
                "1ffb\n" +
                "f247d99883ba1nx_\n" +
                "2d65ElVVwYu8Ufyd\n" +
                "W\n" +
                "OKqnfH\n" +
                "RPxF\n" +
                "i3g~~\n" +
                "1f\n" +
                "fb\n" +
                "f2\n" +
                "47\n" +
                "d9\n" +
                "98\n" +
                "83\n" +
                "ba\n" +
                "1n\n" +
                "x_\n" +
                "2d\n" +
                "65\n" +
                "E\n" +
                "lV\n" +
                "V\n" +
                "w\n" +
                "Y\n" +
                "u8\n" +
                "U\n" +
                "fy\n" +
                "dW\n" +
                "O\n" +
                "K\n" +
                "qn\n" +
                "fH\n" +
                "R\n" +
                "P\n" +
                "xF\n" +
                "i3\n" +
                "g~\n" +
                "~";

        content =Arrays.stream(content.split("\n")).filter(s -> s.matches(".*\\p{IsHan}.*")).collect(Collectors.joining("\n"));
        System.out.println(content);

    }
}
