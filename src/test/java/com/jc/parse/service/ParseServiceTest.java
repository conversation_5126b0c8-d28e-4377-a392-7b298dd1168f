package com.jc.parse.service;

import com.jc.parse.bean.Resume;
import com.jc.parse.service.resolver.Resolver;
import com.jc.parse.service.resolver.ResolverFactory;
import org.aspectj.lang.annotation.Before;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.util.List;
import java.util.concurrent.ForkJoinPool;

/**
 * date : 2025/2/13
 * description :
 *
 * <AUTHOR> zhencai.cheng
 */
class ParseServiceTest {

    private final Logger logger = LoggerFactory.getLogger(ParseServiceTest.class);


    @Before("")
    public void init() {
        System.setProperty("spring.profiles.active", "dev");
    }


    public void parseLocal() throws Exception {
        //路径
        String path = "/User/chengzhencai/Download";
        //过滤器
        String filter = "docx|doc|pdf|jpg|png|jpeg|eml|txt|rar|zip";
        ForkJoinPool pool = new ForkJoinPool(Runtime.getRuntime().availableProcessors());
        File temp = new File("resume.txt");
        if (!temp.exists() && temp.createNewFile()) {
        }
        List<File> files = pool.invoke(new FileProcessor(path, filter));
        for (File file : files) {
            String key = file.getName().substring(file.getName().lastIndexOf(".") + 1);
            try {
                Resolver resolver = ResolverFactory.getParseService(key);
                Resume resume = new Resume();
                resume.setFileName(file.getName());
                resolver.parse(file, resume);
            } catch (Exception e) {
                logger.error("{},解析异常", file.getAbsoluteFile(), e);
            }
        }
    }

}