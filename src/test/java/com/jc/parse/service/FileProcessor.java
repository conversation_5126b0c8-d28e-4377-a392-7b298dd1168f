package com.jc.parse.service;

import com.jc.base.kits.CollectionKit;
import org.apache.commons.lang3.StringUtils;

import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.RecursiveTask;

/**
 * date : 2024/11/4
 * description : 测试收集本地简历文件
 *
 * <AUTHOR> zhencai.cheng
 */

class FileProcessor extends RecursiveTask<List<File>> {

    private final String path;

    private final String filter;

    public FileProcessor(String path, String filter) {
        this.path = path;
        this.filter = filter;
    }

    @Override
    protected List<File> compute() {
        List<File> list = new ArrayList<>();
        List<FileProcessor> tasks = new ArrayList<>();

        File file = new File(path);

        File[] files = file.listFiles();
        if (CollectionKit.isEmpty(files)) {
            return list;
        }
        for (File f : files) {
            if (f.isDirectory()) {
                FileProcessor task = new FileProcessor(f.getAbsolutePath(), filter);
                task.fork();
                tasks.add(task);
            } else {
                if (f.getName().indexOf(".") > 2) {
                    if (StringUtils.isBlank(filter)) {
                        list.add(f);
                    } else {
                        if (f.getName().contains(filter)) {
                            list.add(f);
                        }
                    }
                }
            }
        }
        for (FileProcessor task : tasks) {
            list.addAll(task.join());
        }
        return list;
    }
}
