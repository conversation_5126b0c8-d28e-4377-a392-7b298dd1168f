package com.jc.parse.service;

import com.jc.base.exception.BusinessException;
import com.jc.parse.bean.bo.EmailAccount;
import com.jc.parse.bean.bo.EmailProtocol;
import jakarta.annotation.Resource;

import javax.mail.Session;
import java.util.List;

/**
 * date : 2025/2/13
 * description :
 *
 * <AUTHOR> zhencai.cheng
 */
class EmailServiceTest {


    @Resource
    private EmailService emailService;

    /**
     * 手动获取邮件附件
     */

    public void emailTest() throws BusinessException {
        EmailProtocol emailProtocol = new EmailProtocol();
        emailProtocol.setHost("imap.mxhichina.com");
        emailProtocol.setPort("993");
        emailProtocol.setProtocol("imaps");
        emailProtocol.setEnableSsl("true");
        EmailAccount account = new EmailAccount();
        account.setEmail("<EMAIL>");
        account.setEmailPassword("xxx.");
        account.setAccountId(1L);
        emailProtocol.setEmailList(List.of(account));
        Session session = emailService.createSession(emailProtocol);
        emailService.parseEmail(session, account, emailProtocol);
    }

    /**
     * 测试7天无数据同步检测
     */
    public void testNoDataSyncCheck() {
        // 这个测试需要在有Redis环境的情况下运行
        // 主要测试逻辑在EmailService.handleNoDataSyncCheck方法中
        System.out.println("测试7天无数据同步检测功能");
        System.out.println("1. 当邮箱有新简历时，会更新最后同步时间");
        System.out.println("2. 当邮箱没有新简历且超过7天时，会发送告警消息");
        System.out.println("3. 告警消息24小时内不会重复发送");
    }
}