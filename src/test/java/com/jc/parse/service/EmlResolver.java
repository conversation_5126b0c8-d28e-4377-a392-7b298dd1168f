package com.jc.parse.service;

import com.jc.parse.bean.Resume;
import com.jc.parse.constants.Channel;
import com.jc.parse.kits.EmailKit;
import com.jc.parse.service.resolver.Resolver;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.mail.Session;
import javax.mail.internet.MimeMessage;
import java.io.File;
import java.io.FileInputStream;
import java.util.Properties;

/**
 * date : 2024/11/7
 * description :
 *
 * <AUTHOR> zhencai.cheng
 */
@Service
public class EmlResolver implements Resolver {

    @Value("${parse.resume.path}")
    private String resumePath;

    @Override
    public String getKey() {
        return "eml";
    }

    @Override
    public void parse(File file, Resume resume) throws Exception {
        resume.setFileName(file.getName());
        this.parseEml(file, resume);
    }

    @Override
    public void resolve(File file, Resume resume) throws Exception {
        resume.setFileName(file.getName());
        this.parseEml(file, resume);
    }


    private void parseEml(File file, Resume resume) throws Exception {
        FileInputStream fis = new FileInputStream(file);

        Properties properties = new Properties();
        Session session = Session.getDefaultInstance(properties, null);

        MimeMessage message = new MimeMessage(session, fis);
        //判断哪个渠道发送的邮件
        Channel channel = EmailKit.getChannel(message);
        EmailKit.parseMessage(message, resume, channel, resumePath);
        resume.setResumeSource(channel.getValue());
    }
}
