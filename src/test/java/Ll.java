/**
 * date : 2025/2/27
 * description :
 *
 * <AUTHOR> zhencai.cheng
 */
public class Ll {

    public static void main(String[] args) {
        String content = "丁子秋 \n" +
                "邮箱：<EMAIL> | 手机：（86）15810782652 \n" +
                "教育背景                                                                                                            \n" +
                "集美大学                                                                                                  中国，厦门  \n" +
                "掌握技能                                                                                           \n" +
                "⚫ 熟悉Hadoop的中HDFS的架构和读写流程，MapReduce的优化，Yarn资源调度架构和工作流程 \n" +
                "⚫ 熟悉Kafka的架构、使用、技术选型、环境搭建并对其进行性能调优 \n" +
                "⚫ 熟悉Hive进行离线数仓分层和数仓离线指标分析及其性能调优 \n" +
                "⚫ 熟悉Flink的窗口机制、状态机制、时间语义以及CEP构建 \n" +
                "⚫ 熟练使用Sqoop进行数据传输，处理 \n" +
                "⚫ 熟悉Spark进行离线计算和实时计算及其性能调优 \n" +
                "⚫ 熟练使用MySQl并对其进行调优 \n" +
                "⚫ 熟悉Doris数据库的部署和使用 \n" +
                "⚫ 能够使用CDH搭建大数据管理系统，优化资源配置 \n" +
                "⚫ 掌握Linux的常用命令及Shell脚本的编写 \n" +
                "⚫  熟悉java和scala语言进行大数据开发 \n" +
                "⚫  熟悉python进行脚本及自动化开发 \n" +
                "⚫  部署Prometheus和Grafana进行监控 \n" +
                "⚫  熟悉使用Seatunnel进行数据传输和处理 \n" +
                " \n" +
                "工作经历                                                                                         \n" +
                " \n" +
                "远盟康健科技（北京）有限公司 \n" +
                "大数据开发工程师                        2021.09-2023.11 \n" +
                "⚫ 负责公司整个离线仓库从采集到展示的构建及优化，参与数据集成工具的构建  \n" +
                " \n" +
                "北京惠买在线网络科技有限公司                                                                                      \n" +
                "大数据开发工程师                                                                                     2020.06-2021.08 \n" +
                "⚫ 负责参与公司整个离线仓库从采集到存储的搭建及优化，参与实时数仓的构建 \n" +
                " \n" +
                "项目经历                                                                                         \n" +
                "远盟普惠数据分析平台                       2021.09-2023.11 \n" +
                "大数据开发工程师 \n" +
                "软件架构: DCT+FlinkCDC+Flink+Kafka+CDH+DophinScheduler+KUDU+Hive+DAAS+Seatunnel+Doris+Prometheus+Grafana \n" +
                "项目描述: \n" +
                " 由于业务数据的膨胀与分析需求的产生，构建数据分析平台，数据来源是上游的多个系统数据库和行为数据，并为了决策层能\n" +
                "更好的对活动进行总结和未来方向的制定，构建了数据仓库除此之外由于实时需求的产生，构建实时任务。 \n" +
                "职责描述： \n" +
                "⚫ 参与数据数据中台和数据库的选型 \n" +
                "⚫ 数据模型的设计和建设 \n" +
                "⚫ 负责数据仓库分层与搭建 \n" +
                "⚫ 负责 DWS 层各宽表的搭建 \n" +
                "⚫ 负责 Doris 数据库的部署和数据传输任务的构建 \n" +
                "⚫ 负责 Doris 数据库各表的设计 \n" +
                "⚫ 负责 Doris 数据库的参数调整和监控 \n" +
                "⚫ 负责整个流程的调度设计和日常运维 \n" +
                "⚫ 负责实时任务的开发和部署 \n" +
                "技术描述: \n" +
                "1. 通过 DCT 将上游多个类型的数据库数据抽取到 Hive 的 ODS 层，并在 DWD 层将数据进行简单的清理。并在 DCT 中的，\n" +
                "使用其告警功能对每个任务进行配置。 \n" +
                "2. 依照业务逻辑，建立 DIM 维度层，存储诸如产品信息、项目信息、服务元素信息表等通用性的维度数据。 \n" +
                "3. 基于 DWD 层数据和 DIM 层数据，在 DWS 层进行业用户分析、主数据分析、成本分析等不同业务逻辑的宽表构建。 \n" +
                "4. 在 ADS 层编写用户 7 日内连续登录数、留存率、流失率、客户保险所含服务使用数、医生坐席置忙率等指标，通过 Sqoop\n" +
                "将数据分为全量和增量的同步 Doris 当中用于前端展示。 \n" +
                "5. 使用 DS 来配置每日的流程调度，并配置相应的告警和邮件告知。 \n" +
                "6. 使用 DAAS 平台进行集群资源和任务运行情况监控。 \n" +
                "7. 由于部分业务增添实时性的需求，通过 FlinkCDC 采集上游数据,入到 KUDU 中进行存储。 \n" +
                "8. 实时数据通过 Flink 计算后，会通过 Doris Flink Connector 直接写入到 Doris 中。 \n" +
                "9. 在三台服务器上进行，三 fe 三 be 三副本的集群搭建策略，其中 fe 都是 follower 角色，由于是混布，所以对内存使用量\n" +
                "进行调优。 \n" +
                "10. 由于到上游数据库的增多和业务数据量的变大，更换数据同步方式以缩短每日数据同步时间，这里采用 Seatunnel 进行每\n" +
                "日的数据同步，大表数据传输速度提高 10 倍。 \n" +
                "11. 通过 Prometheus 和 Grafana 对 doris 集群进行监控，并配置了告警，如若出现问题会以邮件的方式提醒。 \n" +
                "  \n" +
                " \n" +
                "惠买实时购数据仓库                                                                                                    \n" +
                "大数据开发工程师                                                                                     2021.01-2021.08 \n" +
                "软件架构：MaxWell+Kafka+Phoenix+Flink+HBase+Redis+ClickHouse+Sugar \n" +
                "项目描述： \n" +
                "实时数据是根据需求驱动，由于各个节日的突变需求的产生，所以开发实时层，方便更加及时的做出当日数据的分析与展示  \n" +
                "职责描述： \n" +
                "⚫ 参与实时平台的搭建和技术选型 \n" +
                "⚫ 参与各层的指标构建 \n" +
                "⚫ CEP 筛选逻辑构建 \n" +
                "⚫ 配置表表项的构建 \n" +
                "⚫ 参与指标的设计 pv、uv、ujd等，用户行为的统计 \n" +
                "技术描述： \n" +
                "1. 通过MaxWell将业务数据导入到Kafka的ODS业务层，通过SpringBoot的程序调用KafkaAPI将日志数据导入到ODS日志层 \n" +
                "2. 通过声明访问的时间状态来对新老客户的修复 \n" +
                "3. 利用侧输出流的方法对数据进行分流，将启动、曝光流放到侧输出流，将页面流放入到主流输出，结果写回kafka \n" +
                "4. 创建配置表实体类，并在 MySQL 创配置表存储维度表字段，用于区分事实数据和维度数据 \n" +
                "5. 创建反序列化类，通过 FlinkCDC 读取配置表形成广播流，主流和广播流进行连接 \n" +
                "6. 创建动态分流，将维度数据放到维度侧输出流，输出到 HBase，事实数据放到主流，写回到 Kafka 的 DWD 层 \n" +
                "7. 为了之后方便统计，构建宽表。指定水位线用于分别对订单和支付中的数据进行分流，并对分出来的这几个流进行双流\n" +
                "join, 写回到Kafka的DWM层 \n" +
                "8. 针对用户数据，对其进行时间状态过滤出首次访问的用户，写回到Kafka的DWM层 \n" +
                "9. 针对用户跳转数据，利用水位线进行过滤数据后利用CEP再进行二次筛选，侧输出流输出超时数据，主流输出跳转数据到\n" +
                "Kafka的DWM层 \n" +
                "10. 对pv、uv、ujd进行格式转换后，三流union后并后依据水位线和维度进行分组，对各个独立的分组进行开窗聚合，将聚合\n" +
                "的访客数据写道ClickHouse \n" +
                "11. 对点击流、收藏流、加购流等格式转换后，union后并后依据水位线和维度进行分组，对各个独立的分组进行开窗聚合，\n" +
                "将聚合的访客数据写道ClickHouse \n" +
                "12. 利用FlinkSQL对地区主题创建动态表，之后通过分组、开窗、聚合后，再转成流数据，构成地区主题表写回到\n" +
                "ClickHouse，关键字主题表类似 \n" +
                "13. 构建数据数据接口，用于可视化展示 \n" +
                "14. 利用Sugar,实现可视化 \n" +
                " \n" +
                "惠买离线数据仓库                                                                                                  \n" +
                "大数据开发工程师                                                                                     2020.06-2020.12 \n" +
                "软件架构：Hadoop+Hive+Spark+Sqoop+MySQL+Azkaban+Kylin+Zabbix+Atlas \n" +
                "项目描述： \n" +
                "由于业务量的增大，数据逐渐增多。为了更好的管理和分析用户的信息，更是为了更方便的为后续的各种购物活动提供数据，\n" +
                "所以需要搭建一个统一管理业务和行为数据的仓库。  \n" +
                "职责描述： \n" +
                "⚫ 参与数据仓库的分层建模与搭建 \n" +
                "⚫ 参与 DWD 层各宽表和拉链表的搭建 \n" +
                "⚫ 公司指标的设计：周回流数据、流失用户、沉默用户、一个月内连续登陆一周用户，按照全部商品、不同类别、不同区域三个\n" +
                "维度的 TopN \n" +
                "⚫ 编写脚本和测试 \n" +
                "技术描述：  \n" +
                "1. HDFS 日志数据直接导入 ODS 层，从 MySQL 传来的业务数据进入业务层，利用采用分区和 Gzip 进行压缩和存储 \n" +
                "2. 对 ODS 的数据进行清洗、脱敏、降维，并导入 DWD 层，并使用 Snappy和 ORC 进行压缩和存储 \n" +
                "3. 根据 DWD 层的数据建立用户宽表、商品宽表、订单宽表，对业务变换缓慢的建立拉链表，导入 DWS 层 \n" +
                "4. 确定 dwd 层的事实，对于日志数据使用 Hive 的系统函数 get_json_object()函数解析日志数据，生成曝光、启动、事件、\n" +
                "页面和错误五张日志表，对于业务数据选取业务线生成对应的业务事实表如支付、退款、订单等事实表 \n" +
                "5. 基于 dwd 层，dws 层对数据进行聚合，形成各个不同的主题宽表，使用列式存储 orc，不采用压缩 \n" +
                "6. ads层通过编写HQL统计用户15天7日连续登录、回流、流失、留存以及商品的订单数、订单金额等指标，采用行式存储，\n" +
                "不采用压缩，将 ads 所统计的指标使用 Sqoop 导入到 MySQL 数据库中 \n" +
                "7. 编写各层数据仓的导入和分析脚本，并依托 Azkaban 进行全流程调度 \n" +
                "8. 使用 Atlas 进行元数据管理 \n" +
                "9. 使用可视化工具 Superset，对数仓的 Hive 表的数据进行可视化展示 \n" +
                "10. 使用即席查询工具 Kylin，即席查询数仓中 Hive 表的数据，进行数据分析 \n" +
                "11. 使用集群监控工具 Zabbix，对集群进行监控，集群出现问题，会以发邮件、电话等方式进行告警 ";

        System.out.println(content.length());
    }
}
