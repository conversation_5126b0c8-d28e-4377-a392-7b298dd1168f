import com.alibaba.fastjson2.JSONObject;
import com.jc.base.exception.HttpException;
import com.jc.base.kits.CollectionKit;
import com.jc.base.kits.HttpKit;
import com.jc.base.kits.MapKit;
import com.jc.parse.constants.Constant;
import net.sourceforge.tess4j.Tesseract;
import net.sourceforge.tess4j.TesseractException;
import org.apache.commons.lang3.StringUtils;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.text.PDFTextStripper;
import org.apache.poi.hwpf.HWPFDocument;
import org.apache.poi.hwpf.extractor.WordExtractor;
import org.apache.poi.xwpf.extractor.XWPFWordExtractor;
import org.apache.poi.xwpf.usermodel.XWPFDocument;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;
import java.util.Properties;
import java.util.Random;

/**
 * date : 2024/10/18
 * description :
 *
 * <AUTHOR> zhencai.cheng
 */
public class Test {


    protected static String parseHost = "http://*************:3000";

    private static String username = "<EMAIL>";

    private static String password = "jingcai##2025";

    public static void main(String[] args) throws Exception {
        String content = "";

//        content = img(new File("/Users/<USER>/Desktop/image/test.png"));
//        content = pdf(new File("/Users/<USER>/Downloads/简历/0402简历/丁子秋_大数据开发.pdf"));

        System.out.println(new Random().nextInt(1000000));
    }

    private static void nlp(String content) throws HttpException {
        // 初始化 Stanford NLP
        Properties props = new Properties();
        props.setProperty("annotators", "tokenize,ssplit,pos,lemma,ner,parse");
        //不处理英文必须false，否则报错
        props.setProperty("ner.useSUTime", "false");
//        StanfordCoreNLP pipeline = new StanfordCoreNLP(props);
//        CoreDocument document = new CoreDocument(content);
//        pipeline.annotate(document);

        // 提取词性标注
//        document.sentences();
//        for (CoreSentence sentence : document.sentences()){
//            System.out.println(sentence.text());
//        }
//         提取命名实体
//        for (CoreEntityMention em : document.entityMentions()) {
//            System.out.println("实体: " + em.text() + ", 类型: " + em.entityType()+"属性: " + em.entityType().toLowerCase() + "=" + em.text());
//        }
//        document.tokens().stream().map(CoreLabel::word).forEach(System.out::println);
    }


    private static String pdf(File file) throws IOException {

        PDDocument document = PDDocument.load(file);
        PDFTextStripper stripper = new PDFTextStripper();
        String content = stripper.getText(document);
        System.out.println(content);
        return content;
    }

    private static String docx(File file) throws IOException {
        FileInputStream fis = new FileInputStream(file);
        XWPFDocument document = new XWPFDocument(fis);
        XWPFWordExtractor extractor = new XWPFWordExtractor(document);
        String content = extractor.getText();
        System.out.println("docx解析结果：" + content);
        return content;
    }

    private static String doc(File file) throws IOException {
        FileInputStream fis = new FileInputStream(file);
        HWPFDocument document = new HWPFDocument(fis);
        WordExtractor extractor = new WordExtractor(document);
        String content = extractor.getText();
        System.out.println("doc解析结果：" + content);
        return content;
    }

    private static String img(File file) throws IOException, TesseractException {
        System.setProperty("jna.library.path", "/opt/homebrew/Cellar/tesseract/5.5.0/lib");
        Tesseract tesseract = new Tesseract();
        tesseract.setLanguage("chi_sim");
        String content = tesseract.doOCR(file);
        System.out.println("img解析结果：" + content);
        return content;
    }

    public static String apiRequest(File file, String cookie) throws Exception {

        if (StringUtils.isBlank(cookie)) {
            throw new HttpException("获取token失败");
        }
        JSONObject reponse = HttpKit.upload(
                parseHost + Constant.Api.FILE_URL,
                file,
                null,
                MapKit.ofMap("content-type", "multipart/form-data", "cookie", "token=" + cookie));
        if (CollectionKit.isEmpty(reponse)) {
            throw new HttpException("解析失败");
        }

        String content = reponse.getString("content");
        content = content.replaceAll("\t", "").replaceAll("\\b[a-zA-Z0-9-_~]{12,}\\b", "");
        List<String> lines = Arrays.stream(content.split("\n")).filter(StringUtils::isNotBlank).toList();
        return String.join("\n", lines);
    }

    /**
     * 获取token
     *
     * @return token
     * @throws HttpException 异常
     */
    private static String getToken() throws HttpException {
        String result = HttpKit.postBody(parseHost + Constant.Api.LOGIN_URL,
                MapKit.of("email", username, "password", password));
        if (result.contains("token")) {
            JSONObject response = JSONObject.parseObject(result);
            return response.getString("token");
        } else {
            throw new HttpException("获取token失败");
        }
    }
}
