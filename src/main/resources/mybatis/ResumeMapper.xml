<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jc.parse.persistence.dao.ResumeDao">

    <resultMap type="com.jc.parse.bean.Resume" id="ResumeMap">
        <result property="resumeId" column="resumeId" jdbcType="INTEGER"/>
        <result property="confirmState" column="confirmState" jdbcType="INTEGER"/>
        <result property="fileName" column="fileName" jdbcType="VARCHAR"/>
        <result property="userName" column="userName" jdbcType="VARCHAR"/>
        <result property="birthday" column="birthday" jdbcType="TIMESTAMP"/>
        <result property="sex" column="sex" jdbcType="VARCHAR"/>
        <result property="mobile" column="mobile" jdbcType="VARCHAR"/>
        <result property="email" column="email" jdbcType="VARCHAR"/>
        <result property="address" column="address" jdbcType="VARCHAR"/>
        <result property="education" column="education" jdbcType="VARCHAR"/>
        <result property="educationExperience" column="educationExperience" jdbcType="VARCHAR"/>
        <result property="workExperience" column="workExperience" jdbcType="VARCHAR"/>
        <result property="projectExperience" column="projectExperience" jdbcType="VARCHAR"/>
        <result property="skill" column="skill" jdbcType="VARCHAR"/>
        <result property="intro" column="intro" jdbcType="VARCHAR"/>
        <result property="award" column="award" jdbcType="VARCHAR"/>
        <result property="certificate" column="certificate" jdbcType="VARCHAR"/>
        <result property="jobStatus" column="jobStatus" jdbcType="VARCHAR"/>
        <result property="expectSalary" column="expectSalary" jdbcType="VARCHAR"/>
        <result property="expectCity" column="expectCity" jdbcType="VARCHAR"/>
        <result property="expectJob" column="expectJob" jdbcType="VARCHAR"/>
        <result property="workYear" column="workYear" jdbcType="VARCHAR"/>
        <result property="resumeOwner" column="resumeOwner" jdbcType="INTEGER"/>
        <result property="resumeSource" column="resumeSource" jdbcType="INTEGER"/>
        <result property="enableSync" column="enableSync" jdbcType="INTEGER"/>
        <result property="receivedDate" column="receivedDate" jdbcType="TIMESTAMP"/>
        <result property="createTime" column="createTime" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!--新增所有列-->
    <insert id="insertResume" keyProperty="resumeId" useGeneratedKeys="true">
        insert into "SpiderResume"(
        <if test="confirmState != null">
            "confirmState",
        </if>
        <if test="fileName != null and fileName != ''">
            "fileName",
        </if>
        <if test="userName != null and userName != ''">
            "userName",
        </if>
        <if test="birthday != null">
            "birthday",
        </if>
        <if test="sex != null and sex != ''">
            "sex",
        </if>
        <if test="mobile != null and mobile != ''">
            "mobile",
        </if>
        <if test="email != null and email != ''">
            "email",
        </if>
        <if test="address != null and address != ''">
            "address",
        </if>
        <if test="education != null and education != ''">
            "education",
        </if>
        <if test="educationExperience != null and educationExperience != ''">
            "educationExperience",
        </if>
        <if test="workExperience != null and workExperience != ''">
            "workExperience",
        </if>
        <if test="projectExperience != null and projectExperience != ''">
            "projectExperience",
        </if>
        <if test="skill != null and skill != ''">
            "skill",
        </if>
        <if test="intro != null and intro != ''">
            "intro",
        </if>
        <if test="award != null and award != ''">
            "award",
        </if>
        <if test="certificate != null and certificate != ''">
            "certificate",
        </if>
        <if test="jobStatus != null and jobStatus != ''">
            "jobStatus",
        </if>
        <if test="expectSalary != null and expectSalary != ''">
            "expectSalary",
        </if>
        <if test="expectCity != null and expectCity != ''">
            "expectCity",
        </if>
        <if test="expectJob != null and expectJob != ''">
            "expectJob",
        </if>
        <if test="workYear != null and workYear != ''">
            "workYear",
        </if>
        <if test="resumeOwner != null">
            "resumeOwner",
        </if>
        <if test="resumeSource != null">
            "resumeSource",
        </if>
        <if test="enableSync != null">
            "enableSync",
        </if>
        <if test="receivedDate != null">
            "receivedDate",
        </if>
        <if test="createTime != null">
            "createTime",
        </if>
        <if test="updateTime != null">
            "updateTime"
        </if>
        )
        values (
        <if test="confirmState != null">
            #{confirmState},
        </if>
        <if test="fileName != null and fileName != ''">
            #{fileName},
        </if>
        <if test="userName != null and userName != ''">
            #{userName},
        </if>
        <if test="birthday != null">
            #{birthday},
        </if>
        <if test="sex != null and sex != ''">
            #{sex},
        </if>
        <if test="mobile != null and mobile != ''">
            #{mobile},
        </if>
        <if test="email != null and email != ''">
            #{email},
        </if>
        <if test="address != null and address != ''">
            #{address},
        </if>
        <if test="education != null and education != ''">
            #{education},
        </if>
        <if test="educationExperience != null and educationExperience != ''">
            #{educationExperience},
        </if>
        <if test="workExperience != null and workExperience != ''">
            #{workExperience},
        </if>
        <if test="projectExperience != null and projectExperience != ''">
            #{projectExperience},
        </if>
        <if test="skill != null and skill != ''">
            #{skill},
        </if>
        <if test="intro != null and intro != ''">
            #{intro},
        </if>
        <if test="award != null and award != ''">
            #{award},
        </if>
        <if test="certificate != null and certificate != ''">
            #{certificate},
        </if>
        <if test="jobStatus != null and jobStatus != ''">
            #{jobStatus},
        </if>
        <if test="expectSalary != null and expectSalary != ''">
            #{expectSalary},
        </if>
        <if test="expectCity != null and expectCity != ''">
            #{expectCity},
        </if>
        <if test="expectJob != null and expectJob != ''">
            #{expectJob},
        </if>
        <if test="workYear != null and workYear != ''">
            #{workYear},
        </if>
        <if test="resumeOwner != null">
            #{resumeOwner},
        </if>
        <if test="resumeSource != null">
            #{resumeSource},
        </if>
        <if test="enableSync != null">
            #{enableSync},
        </if>
        <if test="receivedDate != null">
            #{receivedDate},
        </if>
        <if test="createTime != null">
            #{createTime},
        </if>
        <if test="updateTime != null">
            #{updateTime}
        </if>
        )
    </insert>
</mapper>

