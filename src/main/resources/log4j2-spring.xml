<?xml version="1.0" encoding="UTF-8"?>
<!--Configuration后面的status，这个用于设置log4j2自身内部的信息输出，可以不设置，当设置成trace时，你会看到log4j2内部各种详细输出-->
<!--monitorInterval：Log4j能够自动检测修改配置 文件和重新配置本身，设置间隔秒数-->
<configuration monitorInterval="6">
    <!--日志级别以及优先级排序: OFF > FATAL > ERROR > WARN > INFO > DEBUG > TRACE > ALL -->
    <!--变量配置-->
    <Properties>
        <!-- 定义日志存储的路径 -->
        <property name="LOG_HOME" value="/opt/logs"/>
        <property name="PROJECT_NAME" value="spider-parse"/>
    </Properties>

    <appenders>
        <console name="Console" target="SYSTEM_OUT">
            <PatternLayout
                    pattern="%d %highlight{%p}{ERROR=Bright RED, WARN=Bright Yellow, INFO=Bright Green, DEBUG=<PERSON>an, TRACE=Bright White} %style{[%thread]}{bright,magenta} %style{%c:%line)}{cyan}:- %msg -%n"/>
        </console>

        <!-- 这个会打印出所有的info及以下级别的信息，每次大小超过size，则这size大小的日志会自动存入按年份-月份建立的文件夹下面并进行压缩，作为存档-->
        <RollingFile name="RollingFileInfo"
                     fileName="${LOG_HOME}/${PROJECT_NAME}/${PROJECT_NAME}-info.log"
                     filePattern="${LOG_HOME}/${PROJECT_NAME}/${date:yyyy-MM-dd}/${PROJECT_NAME}-info-%d{yyyy-MM-dd-HH}.log"
                     bufferedIO="true"
                     bufferSize="16384"
                     immediateFlush="true"
                     ignoreExceptions="false">
            <Filters>
                <ThresholdFilter level="INFO"/>
                <ThresholdFilter level="WARN" onMatch="DENY" onMismatch="NEUTRAL"/>
            </Filters>
            <PatternLayout
                    pattern="%d %highlight{%p}{ INFO=Bright Green} %style{[%thread]}{bright,magenta} %style{%c:%line)}{cyan}:- %msg -%n"/>
            <Policies>
                <TimeBasedTriggeringPolicy interval="1" modulate="true"/>
            </Policies>
        </RollingFile>

        <RollingFile name="RollingFileWarn"
                     fileName="${LOG_HOME}/${PROJECT_NAME}/${PROJECT_NAME}-warn.log"
                     filePattern="${LOG_HOME}/${PROJECT_NAME}/${date:yyyy-MM-dd}/${PROJECT_NAME}-warn-%d{yyyy-MM-dd-HH}.log"
                     bufferedIO="true"
                     bufferSize="8192"
                     immediateFlush="true"
                     ignoreExceptions="false">
            <Filters>
                <ThresholdFilter level="WARN"/>
                <ThresholdFilter level="ERROR" onMatch="DENY" onMismatch="NEUTRAL"/>
            </Filters>
            <PatternLayout
                    pattern="%d %highlight{%p}{WARN=Bright Yellow} %style{[%thread]}{bright,magenta} %style{%c:%line)}{cyan}:- %msg -%n"/>
            <Policies>
                <TimeBasedTriggeringPolicy interval="1" modulate="true"/>
            </Policies>
        </RollingFile>

        <RollingFile name="RollingFileError"
                     fileName="${LOG_HOME}/${PROJECT_NAME}/${PROJECT_NAME}-error.log"
                     filePattern="${LOG_HOME}/${PROJECT_NAME}/${date:yyyy-MM-dd}/${PROJECT_NAME}-error-%d{yyyy-MM-dd-HH}.log"
                     bufferedIO="true"
                     bufferSize="8192"
                     immediateFlush="true"
                     ignoreExceptions="false">
            <ThresholdFilter level="ERROR"/>
            <PatternLayout
                    pattern="%d %highlight{%p}{ERROR=Bright RED} %style{[%thread]}{bright,magenta} %style{%c:%line)}{cyan}:- %msg -%n"/>
            <Policies>
                <TimeBasedTriggeringPolicy interval="1" modulate="true"/>
            </Policies>
        </RollingFile>

    </appenders>

    <loggers>
        <logger name="org.springframework" level="error"/>
        <logger name="org.springframework.cloud.config" level="info"/>
        <logger name="com.netflix" level="error"/>
        <logger name="org.hibernate" level="error"/>
        <!--<logger name="org.apache.ibatis" level="info"/>-->
        <logger name="okhttp3" level="warn"/>
        <logger name="org.apache.http" level="error"/>
        <logger name="io.netty" level="info"/>
        <logger name="springfox.documentation" level="error"/>
        <logger name="com.jc.base.config.feign.FeignHeadConfiguration" level="error"/>
        <logger name="druid.sql.Statement" level="debug" additivity="false">
            <appender-ref ref="Console"/>
        </logger>
        <logger name="druid.sql.ResultSet" level="debug" additivity="false">
            <appender-ref ref="Console"/>
        </logger>

        <logger name="org.mybatis" level="info" additivity="false">
            <AppenderRef ref="Console"/>
        </logger>

        <root level="info">
            <appender-ref ref="RollingFileInfo"/>
            <appender-ref ref="RollingFileWarn"/>
            <appender-ref ref="RollingFileError"/>
            <appender-ref ref="Console"/>
        </root>
    </loggers>

</configuration>

