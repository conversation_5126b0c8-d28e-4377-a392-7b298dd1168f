server:
  port: 8781
spring:
  profiles:
    active: @env@
  application:
    name: spider-parse
  main:
    allow-bean-definition-overriding: true
  cloud:
    config:
      profile: @env@
      label: release
      name: common,redis,lettuce,db,ocr
  feign:
    multipart:
      enabled: true
  task:
    execution:
      pool:
        max-size: 5
        core-size: 2
        queue-capacity: 600
parse:
  host: http://localhost:3000
  username: ch<PERSON><PERSON><PERSON><PERSON>@zhongtenghr.com
  password: xxxx
  resume:
    path: /opt/file/resume
  encrypt:
    keys:
      - mobile1
    owner-keys:
      - createBy
      - resumeOwner
  dic-mapping:
    status:
      - Status
