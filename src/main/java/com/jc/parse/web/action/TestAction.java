package com.jc.parse.web.action;

import com.alibaba.fastjson2.JSONObject;
import com.jc.base.bean.ResponseJson;
import com.jc.base.kits.FileKit;
import com.jc.parse.service.EmailService;
import com.jc.parse.service.EmailSyncTimeService;
import com.jc.parse.service.ParseService;
import com.jc.web.BaseController;
import jakarta.annotation.Resource;
import net.sourceforge.tess4j.Tesseract;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.util.List;

/**
 * date : 2025/2/25
 * description :
 *
 * <AUTHOR> zhencai.cheng
 */
@RestController
public class TestAction extends BaseController {


    @Value("${ocr.tesseract.path}")
    private String tesseractPath;


    @RequestMapping("/data/image/parse")
    public ResponseJson<Object> parse(@RequestPart(value = "file", required = false) MultipartFile file) throws Exception {
        System.setProperty("jna.library.path", tesseractPath);
        Tesseract tesseract = new Tesseract();
        tesseract.setLanguage("chi_sim");
        File target = FileKit.transferTo(file, ".png");
        String content = tesseract.doOCR(target);
        target.delete();
        return wrapper(content);
    }

    @Resource
    private EmailService emailService;

    /**
     * 单独采集邮箱
     *
     * @return 处理结果
     * @throws Exception 异常
     */
    @PostMapping("/data/spider/email/collect")
    public ResponseJson<Object> collectEmail(@RequestBody String params) throws Exception {
        JSONObject requestMap = this.parse(params, "email");
        emailService.collectEmail(requestMap.getString("email"));
        return wrapper();
    }

    @Resource
    private ParseService parseService;

    /**
     * 解析简历
     */
    @RequestMapping("/data/spider/parse")
    public ResponseJson<Object> parse(@RequestPart(value = "file", required = false) MultipartFile file,
                                      String params) throws Exception {
        JSONObject requestMap = this.parse(params, "email");
        parseService.parse(file, requestMap);
        return wrapper();
    }

    @Resource
    private EmailSyncTimeService emailSyncTimeService;

    /**
     * 批量清除邮箱同步失败记录
     *
     * @param params 请求参数，包含邮箱列表
     * @return 处理结果
     */
    @PostMapping("/data/email/clear-failures")
    public ResponseJson<Object> clearEmailSyncFailures(@RequestBody String params) {
        try {
            JSONObject requestMap = this.parse(params, "emails");
            List<String> emails = requestMap.getList("emails", String.class);
            
            if (emails != null && !emails.isEmpty()) {
                for (String email : emails) {
                    emailSyncTimeService.clearSyncFailure(email);
                }
                return wrapper("成功清除 " + emails.size() + " 个邮箱的同步失败记录");
            } else {
                return wrapper("邮箱列表为空");
            }
        } catch (Exception e) {
            return wrapper("清除失败记录失败: " + e.getMessage());
        }
    }

    /**
     * 批量设置邮箱为非首次同步状态
     *
     * @param params 请求参数，包含邮箱列表
     * @return 处理结果
     */
    @PostMapping("/data/email/set-non-first-sync")
    public ResponseJson<Object> setEmailsNonFirstSync(@RequestBody String params) {
        try {
            JSONObject requestMap = this.parse(params, "emails");
            List<String> emails = requestMap.getList("emails", String.class);
            
            if (emails != null && !emails.isEmpty()) {
                for (String email : emails) {
                    emailSyncTimeService.setNonFirstSync(email);
                }
                return wrapper("成功设置 " + emails.size() + " 个邮箱为非首次同步状态");
            } else {
                return wrapper("邮箱列表为空");
            }
        } catch (Exception e) {
            return wrapper("设置非首次同步状态失败: " + e.getMessage());
        }
    }

    /**
     * 批量设置邮箱上次成功同步时间
     *
     * @param params 请求参数，包含邮箱列表和同步时间
     * @return 处理结果
     */
    @PostMapping("/data/email/set-last-sync-time")
    public ResponseJson<Object> setEmailsLastSyncTime(@RequestBody String params) {
        try {
            JSONObject requestMap = this.parse(params, "emails", "lastSyncTime");
            List<String> emails = requestMap.getList("emails", String.class);
            Long lastSyncTime = requestMap.getLong("lastSyncTime");
            
            if (emails != null && !emails.isEmpty() && lastSyncTime != null) {
                for (String email : emails) {
                    emailSyncTimeService.setLastSyncTime(email, lastSyncTime);
                }
                return wrapper("成功设置 " + emails.size() + " 个邮箱的上次成功同步时间");
            } else {
                return wrapper("邮箱列表或同步时间为空");
            }
        } catch (Exception e) {
            return wrapper("设置上次成功同步时间失败: " + e.getMessage());
        }
    }

}
