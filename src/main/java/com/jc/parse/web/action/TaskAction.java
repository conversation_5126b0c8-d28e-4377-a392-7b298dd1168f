package com.jc.parse.web.action;

import com.jc.base.bean.ResponseJson;
import com.jc.base.exception.BusinessException;
import com.jc.parse.service.EmailService;
import com.jc.web.BaseController;
import jakarta.annotation.Resource;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * date : 2024/11/1
 * description : 定时任务
 *
 * <AUTHOR> zhencai.cheng
 */
@RestController
public class TaskAction extends BaseController {

    @Resource
    private EmailService emailService;
    @Resource
    private ThreadPoolTaskExecutor taskExecutor;

    /**
     * 定时任务，收集邮件附件
     *
     * @return 采集结果
     */
    @PostMapping("/inner/spider/task/email")
    public ResponseJson<Object> getEmailAttachment() {
        taskExecutor.execute(() -> emailService.parseEmail());
        return wrapper();
    }


}
