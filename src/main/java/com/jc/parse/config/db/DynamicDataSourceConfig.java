package com.jc.parse.config.db;


import com.jc.base.config.db.annotation.AutoDataSource;
import com.jc.base.config.db.config.BaseDataSourceConfig;
import com.jc.base.kits.MapKit;
import com.jc.parse.config.mvc.ProjectProperties;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.boot.autoconfigure.MybatisProperties;
import org.mybatis.spring.mapper.MapperScannerConfigurer;
import org.springframework.aop.Advisor;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.transaction.PlatformTransactionManager;

import javax.sql.DataSource;
import java.sql.SQLException;

/**
 * date : 2025-03-03 15:05:57
 * description : 数据源配置
 *
 * <AUTHOR>
 */
@Configuration
@EnableConfigurationProperties({ReadDataSourceProperties.class, WriteDataSourceProperties.class, ProjectProperties.class})
public class DynamicDataSourceConfig extends BaseDataSourceConfig {

    @Bean
    public DataSource dynamicDataSource(ReadDataSourceProperties readDataSourceProperties,
                                        WriteDataSourceProperties writeDataSourceProperties) throws SQLException {
        DataSource readDataSource = super.buildDataSource(readDataSourceProperties);
        DataSource writeDataSource = super.buildDataSource(writeDataSourceProperties);
        return super.dataSource(readDataSource, "write", MapKit.ofMap("read", writeDataSource));
    }


    @Bean
    public SqlSessionFactory sqlSessionFactory(DataSource dynamicDataSource, ProjectProperties projectProperties) throws Exception {
        MybatisProperties properties = new MybatisProperties();
        properties.setTypeAliasesPackage("com.jc.parse.bean");
        return super.buildSqlSessionFactory(dynamicDataSource, properties, projectProperties.getEncrypt());
    }

    @Bean
    public MapperScannerConfigurer writeScannerConfigurer() {
        return super.buildMapperScanConfigurer("com.jc.parse.persistence.dao", AutoDataSource.class, "sqlSessionFactory");
    }


    @Bean
    @Override
    public PlatformTransactionManager transactionManager(DataSource dynamicDataSource) {
        return new DataSourceTransactionManager(dynamicDataSource);
    }

    @Bean
    public Advisor dynamicDataSourceAdvisor() {
        return super.dynamicDataSourceAdvisor("read", "get", "find", "check", "convert", "export");
    }

    @Bean
    public Advisor transactionManagerAdvisor(PlatformTransactionManager transactionManager) {
        return super.transactionManagerAdvisor(transactionManager, "approve*", "publish*",
                "refund*", "remove*", "charge*", "call*");
    }

}

