package com.jc.parse.config.db;


import com.jc.base.config.db.bean.BaseDataSource;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;

/**
 * date : 2025-03-03 15:05:57
 * description : 读库配置
 *
 * <AUTHOR>
 */
@RefreshScope
@ConfigurationProperties(prefix = "datasource.cloud.read")
public class ReadDataSourceProperties extends BaseDataSource {

}

