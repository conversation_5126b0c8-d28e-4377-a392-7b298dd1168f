package com.jc.parse.config.mvc;

import com.jc.base.config.mvc.BaseInterceptorConfig;
import com.jc.parse.constants.Constant;
import feign.RequestInterceptor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * date : 2024-05-29 12:22:26
 * description : feign请求拦截器配置
 *
 * <AUTHOR>
 */
@Configuration
public class InterceptorConfig extends BaseInterceptorConfig {


    @Bean
    public RequestInterceptor requestInterceptor() {
        return super.requestInterceptor(Constant.PROJECT_NAME);
    }

}

