package com.jc.parse.config.mvc;

import com.jc.base.config.mvc.BaseWebMvcConfig;
import com.jc.base.filter.DataFilter;
import com.jc.base.filter.ParameterFilter;
import com.jc.base.filter.PermissionFilter;
import com.jc.base.filter.TestFilter;
import com.jc.base.persistence.redis.RedisCommonCache;
import com.jc.parse.constants.Constant;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.web.servlet.config.annotation.EnableWebMvc;

/**
 * date : 2024-05-29 12:22:26
 * description : mvc配置
 *
 * <AUTHOR>
 */
@Configuration
@EnableWebMvc
public class WebMvcConfig extends BaseWebMvcConfig {

    private final RedisCommonCache redisCommonCache;

    public WebMvcConfig(RedisCommonCache redisCommonCache) {
        this.redisCommonCache = redisCommonCache;
    }

    @Bean
    public FilterRegistrationBean<ParameterFilter> parameterFilterFilterRegistrationBean() {
        return super.parameterFilterFilterRegistrationBean(Constant.PROJECT_NAME);
    }


    @Profile({"dev", "test", "online"})
    @Bean
    public FilterRegistrationBean<PermissionFilter> permissionFilterFilterRegistrationBean() {
        FilterRegistrationBean<PermissionFilter> registration =
                super.permissionFilterRegistrationBean(redisCommonCache, Constant.sessionName);
        registration.setOrder(Integer.MAX_VALUE - 3);
        return registration;
    }

    @Profile({"dev", "test", "online"})
    @Bean
    public FilterRegistrationBean<DataFilter> dataFilterFilterRegistrationBean() {
        FilterRegistrationBean<DataFilter> registration = super.dataFilterRegistrationBean(redisCommonCache, Constant.sessionName);
        registration.setOrder(Integer.MAX_VALUE - 4);
        return registration;
    }

    @Profile("local")
    @Bean
    public FilterRegistrationBean<TestFilter> testFilter() {
        TestFilter filter = new TestFilter();
        FilterRegistrationBean<TestFilter> registration = new FilterRegistrationBean<>(filter);
        registration.addUrlPatterns("*");
        registration.setName("testFilter");
        registration.setOrder(Integer.MAX_VALUE - 10);
        return registration;
    }
}

