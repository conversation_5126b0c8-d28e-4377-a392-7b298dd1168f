package com.jc.parse.config.redis;

import com.jc.base.config.redis.config.BasicRedisConfig;
import com.jc.base.persistence.redis.RedisCache;
import com.jc.base.persistence.redis.RedisCommonCache;
import com.jc.base.persistence.redis.RedisQueue;
import com.jc.parse.constants.Constant;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.annotation.Primary;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.connection.jedis.JedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;

/**
 * date : 2024-05-29 12:22:26
 * description : redis 缓存配置
 *
 * <AUTHOR>
 */
@Configuration
@EnableConfigurationProperties({RedisCacheProperties.class})
public class RedisCacheConfig extends BasicRedisConfig {

    @Bean
    @Primary
    public RedisConnectionFactory cacheJedisConnectionFactory(RedisCacheProperties properties) {
        return super.buildJedisConnectionFactory(properties);
    }

    @Bean
    @Primary
    public RedisTemplate<String, String> redisTemplate(RedisConnectionFactory cacheJedisConnectionFactory) {
        return super.buildRedisTemplate(cacheJedisConnectionFactory);
    }

    @Bean
    public RedisCache redisCache(RedisTemplate<String, String> redisTemplate) {
        return new RedisCache(redisTemplate, Constant.PROJECT_NAME);
    }

    @Lazy
    @Bean
    public RedisCommonCache redisCommonCache() {
        return new RedisCommonCache();
    }


    @Lazy
    @Bean
    public RedisQueue redisQueue(RedisTemplate<String, String> redisTemplate) {
        return new RedisQueue(redisTemplate);
    }

}

