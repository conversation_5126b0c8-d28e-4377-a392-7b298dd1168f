package com.jc.parse.config.redis;

import com.jc.base.config.redis.bean.BaseRedis;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;

/**
 * date : 2024-05-29 12:22:26
 * description : redis属性
 *
 * <AUTHOR>
 */
@RefreshScope
@ConfigurationProperties(prefix = "redis.cache")
public final class RedisCacheProperties extends BaseRedis {


}

