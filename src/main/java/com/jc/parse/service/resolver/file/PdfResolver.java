package com.jc.parse.service.resolver.file;

import com.jc.parse.bean.Resume;
import com.jc.parse.service.resolver.AbstractResolver;
import org.apache.commons.lang3.StringUtils;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.text.PDFTextStripper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.io.File;
import java.util.Arrays;
import java.util.stream.Collectors;

/**
 * date : 2024/10/18
 * description : pdf解析
 *
 * <AUTHOR> zhencai.cheng
 */
@Service
public class PdfResolver extends AbstractResolver {
    private final Logger logger = LoggerFactory.getLogger(PdfResolver.class);

    @Override
    public String getKey() {
        return "pdf";
    }

    @Override
    public void parse(File file, Resume resume) throws Exception {
        String content = apiRequest(file);
        if (StringUtils.isBlank(content)) {
            logger.error("解析 {} 异常,内容为空", resume.getFileName());
            return;
        }
        content =Arrays.stream(content.split("\n")).filter(s -> s.matches(".*\\p{IsHan}.*")).collect(Collectors.joining("\n"));
        super.standard(content, resume);
    }

    @Override
    public void resolve(File file, Resume resume) throws Exception {
        PDDocument document = PDDocument.load(file);
        PDFTextStripper stripper = new PDFTextStripper();
        String content = stripper.getText(document);
        document.close();
        content = Arrays.stream(content.split("\n")).filter(s -> s.matches(".*\\p{IsHan}.*")).collect(Collectors.joining("\n"));
        super.standard(content, resume);
    }
}
