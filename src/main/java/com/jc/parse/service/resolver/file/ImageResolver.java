package com.jc.parse.service.resolver.file;

import com.jc.parse.bean.Resume;
import com.jc.parse.service.resolver.AbstractResolver;
import net.sourceforge.tess4j.Tesseract;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.File;

/**
 * date : 2024/10/18
 * description : 图片解析
 *
 * <AUTHOR> zhencai.cheng
 */
@Service
public class ImageResolver extends AbstractResolver {

    @Value("${ocr.tesseract.path}")
    private String tesseractPath;

    @Override
    public String getKey() {
        return "image";
    }

    @Override
    public void parse(File file, Resume resume) throws Exception {
        System.setProperty("jna.library.path", tesseractPath);
        Tesseract tesseract = new Tesseract();
        tesseract.setLanguage("chi_sim");
        String content = tesseract.doOCR(file);
        super.standard(content, resume);
    }

    @Override
    public void resolve(File file, Resume resume) throws Exception {
        System.setProperty("jna.library.path", tesseractPath);
        Tesseract tesseract = new Tesseract();
        tesseract.setLanguage("chi_sim");
        String content = tesseract.doOCR(file);
        super.standard(content, resume);
    }
}
