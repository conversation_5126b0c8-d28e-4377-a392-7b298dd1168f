package com.jc.parse.service.resolver.file;

import com.jc.parse.bean.Resume;
import com.jc.parse.service.resolver.AbstractResolver;
import org.apache.poi.xwpf.extractor.XWPFWordExtractor;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.FileInputStream;

/**
 * date : 2024/10/22
 * description : 2007-docx解析
 *
 * <AUTHOR> zhencai.cheng
 */
@Service
public class DocxResolver extends AbstractResolver {
    @Override
    public String getKey() {
        return "docx";
    }

    @Override
    public void parse(File file, Resume resume) throws Exception {
        String content = apiRequest(file);
        super.standard(content, resume);
    }

    @Override
    public void resolve(File file, Resume resume) throws Exception {
        FileInputStream fis = new FileInputStream(file);
        XWPFDocument document = new XWPFDocument(fis);
        XWPFWordExtractor extractor = new XWPFWordExtractor(document);
        String content = extractor.getText();
        fis.close();
        super.standard(content, resume);
    }
}
