package com.jc.parse.service.resolver;

import com.jc.parse.bean.Resume;
import org.springframework.beans.factory.InitializingBean;

import java.io.File;

/**
 * date : 2024/10/18
 * description : 解析服务
 *
 * <AUTHOR> zhencai.cheng
 */
public interface Resolver extends InitializingBean {

    /**
     * 服务key
     *
     * @return key
     */
    String getKey();

    /**
     * 解析文件，通过open web api解析
     *
     * @param file 文件
     * @return 解析结果
     */
    void parse(File file, Resume resume) throws Exception;

    /**
     * 解析文件，用对文件类型解析
     *
     * @param file 文件
     * @return 解析结果
     */
    void resolve(File file, Resume resume) throws Exception;


    @Override
    default void afterPropertiesSet() throws Exception {
        ResolverFactory.register(getKey(), this);
    }
}
