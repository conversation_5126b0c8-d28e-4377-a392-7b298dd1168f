package com.jc.parse.service.resolver;

import com.jc.parse.constants.Channel;
import com.jc.parse.service.resolver.email.ChannelResolver;

import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;

/**
 * date : 2024/10/18
 * description : 解析工厂
 *
 * <AUTHOR> zhencai.cheng
 */
public class ResolverFactory {
    /**
     * 各种文件解析
     */
    private static final Map<String, Resolver> SERVICES_MAP = new ConcurrentHashMap<>();
    /**
     * 各种渠道解析
     */
    private static final Map<Channel, ChannelResolver> EMAIL_RESOLVER_MAP = new ConcurrentHashMap<>();

    public static void register(String key, Resolver resolver) {
        SERVICES_MAP.put(key, resolver);
    }

    public static Resolver getParseService(String key) {
        Resolver service = SERVICES_MAP.get(key);
        if (Objects.isNull(service)) {
            throw new RuntimeException("未找到对应的解析服务");
        }
        return service;
    }

    public static void registerEmail(Channel key, ChannelResolver resolver) {
        EMAIL_RESOLVER_MAP.put(key, resolver);
    }

    public static ChannelResolver getEmailResolver(Channel key) {
        ChannelResolver resolver = EMAIL_RESOLVER_MAP.get(key);
        if (Objects.isNull(resolver)) {
            throw new RuntimeException("未找到对应的解析服务");
        }
        return resolver;
    }
}
