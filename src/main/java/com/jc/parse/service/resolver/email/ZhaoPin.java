package com.jc.parse.service.resolver.email;

import com.jc.base.kits.CollectionKit;
import com.jc.base.kits.DateKit;
import com.jc.parse.bean.Resume;
import com.jc.parse.bean.bo.EmailContent;
import com.jc.parse.constants.Channel;
import com.jc.parse.constants.Patterned;
import com.jc.parse.kits.ParseKit;
import org.apache.commons.lang3.StringUtils;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.regex.Pattern;

/**
 * date : 2024/11/1
 * description : 智联招聘
 *
 * <AUTHOR> zhencai.cheng
 */
@Service
public class ZhaoPin implements ChannelResolver {

    private final Logger logger = LoggerFactory.getLogger(ZhaoPin.class);

    @Override
    public Channel getKey() {
        return Channel.ZHAO_PIN;
    }

    @Override
    public void parse(EmailContent content, Resume resume) throws Exception {
        this.parseHtml(content, resume);
    }

    private void parseHtml(EmailContent content, Resume resume) {
        Document document = Jsoup.parse(content.getHtml());

        this.parseExperience(document, resume);
    }


    private void parseExperience(Document document, Resume resume) {
        String content = document.text();
        if (StringUtils.isBlank(content)) {
            return;
        }
        String regx = "%s\\s+(.*?)\\s+%s";
        String[] ary = CollectionKit.ofArray(">", "求职意向", "教育经历", "工作经历", "去回复");
        // 基本信息
        String temp = ParseKit.match(content, Pattern.compile(String.format(regx, ary[0], ary[1])), 1);
        this.parseBase(temp, resume);
        if (content.contains(ary[2])) {
            //求职意向
            temp = ParseKit.match(content, Pattern.compile(String.format(regx, ary[1], ary[2])), 1);
            resume.setExpectJob(temp);
            //教育经历
            temp = ParseKit.match(content, Pattern.compile(String.format(regx, ary[2], ary[3])), 1);
            resume.setEducationExperience(temp);
        } else {
            //求职意向
            temp = ParseKit.match(content, Pattern.compile(String.format(regx, ary[1], ary[3])), 1);
            resume.setExpectJob(temp);
        }
        //工作经历
        temp = ParseKit.match(content, Pattern.compile(String.format(regx, ary[3], ary[4])), 1);
        resume.setWorkExperience(temp);

    }

    private void parseEducation(String content, String regex) {
        if (StringUtils.isBlank(content)
                || StringUtils.isBlank(regex)
                || !content.contains(regex)) {
            return;
        }
        String[] split = content.split(regex);
        if (split.length < 2) {
            return;
        }
        String temp = split[1];
    }

    /**
     * 解析基本信息
     *
     * @param content 文本内空
     * @param resume  简历对象
     */
    private void parseBase(String content, Resume resume) {
        if (StringUtils.isBlank(content)) {
            return;
        }
        if (content.contains(">")) {
            content = content.split(">")[1];
        }
        String temp = ParseKit.match(content, Patterned.name.getHtml());
        if (StringUtils.isNotBlank(temp)) {
            resume.setUserName(temp);
        }
        temp = ParseKit.match(content, Patterned.sex.getHtml());
        if (StringUtils.isNotBlank(temp)) {
            resume.setSex(temp);
        }
        temp = ParseKit.match(content, Patterned.age.getHtml());
        if (StringUtils.isNotBlank(temp)) {
            resume.setBirthday(DateKit.parser(temp, "yyyy年MM月"));
        }
        temp = ParseKit.match(content, Patterned.workYear.getHtml(), 1);
        if (StringUtils.isNotBlank(temp)) {
            resume.setWorkYear(temp);
        }
        temp = ParseKit.match(content, Patterned.education.getHtml());
        if (StringUtils.isNotBlank(temp)) {
            resume.setEducation(temp);
        }
        temp = ParseKit.match(content, Patterned.jobStatus.getHtml());
        if (StringUtils.isNotBlank(temp)) {
            resume.setJobStatus(temp);
        }
    }

}
