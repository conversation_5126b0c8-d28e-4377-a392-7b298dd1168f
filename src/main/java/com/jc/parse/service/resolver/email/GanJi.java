package com.jc.parse.service.resolver.email;

import com.alibaba.fastjson2.JSONObject;
import com.jc.base.kits.CollectionKit;
import com.jc.base.kits.DateKit;
import com.jc.parse.bean.Resume;
import com.jc.parse.bean.bo.EmailContent;
import com.jc.parse.constants.Channel;
import net.sourceforge.tess4j.Tesseract;
import net.sourceforge.tess4j.TesseractException;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.IOException;
import java.net.URI;
import java.net.URL;
import java.util.List;
import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * date : 2024/11/1
 * description : 赶集 解析器
 *
 * <AUTHOR> zhencai.cheng
 */
@Service
public class GanJi implements ChannelResolver {

    @Value("${ocr.tesseract.path}")
    private String tesseractPath;

    private final Logger logger = LoggerFactory.getLogger(GanJi.class);

    private final Pattern pattern = Pattern.compile("(\\p{IsHan}+)（(男|女)，(\\d{1,3})岁）");

    @Override
    public Channel getKey() {
        return Channel.GAN_JI;
    }

    @Override
    public void parse(EmailContent content, Resume resume) throws Exception {
        try {
            this.parseHtml(content.getHtml(), resume);
        } catch (Exception e) {
            logger.error("解析58手机号异常异常", e);
        }
    }

    /**
     * 获取手机号
     *
     * @param html html内容
     * @return 手机号
     * @throws IOException        io异常
     * @throws TesseractException tesseract异常
     */
    private void parseHtml(String html, Resume resume) throws IOException, TesseractException {
        Document doc = Jsoup.parse(html);
        Elements elements = doc.select("h3");
        int size = elements.size();

        if (size > 0) {
            //姓名、性别、年龄
            Element element = elements.first();
            Matcher matcher = pattern.matcher(element.text());
            if (matcher.find()) {
                resume.setUserName(matcher.group(1));
                resume.setSex(matcher.group(2));
                resume.setBirthday(DateKit.addYears(-Integer.parseInt(matcher.group(3))));
            }
            //学历
            this.setEducation(element, resume);
        }
        if (size > 1) {
            //求职意向
            this.setExpect(elements.get(1), resume);
        }
        if (size > 2) {
            this.setWorkExperience(elements.get(2), resume);
        }
        elements = doc.select("img");
        // 获取所有包含指定src属性的img元素。图片手机号
        setMobile(elements, resume);
    }

    private void setWorkExperience(Element element, Resume resume) {
        Element parent = element.parent();
        if (Objects.isNull(parent)) {
            return;
        }
        Element sibling = parent.nextElementSibling();
        if (Objects.isNull(sibling)) {
            return;
        }
        JSONObject experienceMap = new JSONObject();
        Elements elements = sibling.select("strong");
        if (!elements.isEmpty()) {
            experienceMap.put("companyName", elements.first().text());
        }
        elements = sibling.select("li");
        int size = elements.size();
        if (size > 0) {
            experienceMap.put("date", elements.getFirst().text());
        }
        if (size > 1) {
            experienceMap.put("position", elements.get(1).text());
        }
        if (size > 2) {
            experienceMap.put("salary", elements.get(2).text());
        }
        elements = sibling.select("tbody");
        if (!elements.isEmpty()) {
            experienceMap.put("content", elements.text());
        }
        resume.setWorkExperience(experienceMap.toJSONString());
    }

    /**
     * 设置学历
     *
     * @param element 元素
     * @param resume  简历对象
     */
    private void setEducation(Element element, Resume resume) {
        Element parent = element.parent();
        if (Objects.isNull(parent)) {
            return;
        }
        //学历
        element = parent.selectFirst("li");
        if (Objects.isNull(element)) {
            return;
        }
        resume.setEducation(element.text());
        //工作年限
        element = element.nextElementSibling();
        if (Objects.isNull(element)) {
            return;
        }
        resume.setWorkYear(element.text());
    }

    /**
     * 设置求职意向
     *
     * @param element 元素
     * @param resume  简历对象
     */
    private void setExpect(Element element, Resume resume) {
        Element parent = element.parent();
        if (Objects.isNull(parent)) {
            return;
        }
        parent = parent.parent();
        if (Objects.isNull(parent)) {
            return;
        }
        Elements lis = parent.select("li");
        for (Element e : lis) {
            String[] ary = e.text().split("：");
            if (ary[0].contains("期望职位")) {
                resume.setExpectJob(ary[1]);
            } else if (ary[0].contains("期望薪资")) {
                resume.setExpectSalary(ary[1]);
            } else if (ary[0].contains("期望地区")) {
                resume.setExpectCity(ary[1]);
            }
        }
    }

    /**
     * 获取手机号
     *
     * @param elements 图片元素
     * @param resume   简历对象
     * @throws IOException        io异常
     * @throws TesseractException tesseract异常
     */
    private void setMobile(Elements elements, Resume resume) throws IOException, TesseractException {
        List<String> list = elements.stream()
                .filter(e -> e.attr("src").contains("https://zpjl.58.com/phone/email"))
                .map(e -> e.attr("src"))
                .toList();
        if (CollectionKit.isEmpty(list)) {
            return;
        }
        //ocr解决图片手机号
        System.setProperty("jna.library.path", tesseractPath);
        Tesseract tesseract = new Tesseract();
        tesseract.setLanguage("chi_sim");
        URL url = URI.create(list.getFirst()).toURL();
        BufferedImage image = ImageIO.read(url);
        String content = tesseract.doOCR(image);
        //提取图片数字
        Matcher matcher = Pattern.compile("\\d+").matcher(content.replace(" ", ""));
        if (matcher.find()) {
            resume.setMobile(matcher.group());
        }
    }

}
