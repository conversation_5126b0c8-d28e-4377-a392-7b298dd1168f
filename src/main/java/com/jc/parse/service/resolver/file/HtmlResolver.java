package com.jc.parse.service.resolver.file;

import com.jc.parse.bean.Resume;
import com.jc.parse.service.resolver.AbstractResolver;
import org.springframework.stereotype.Service;

import java.io.File;

/**
 * date : 2024/10/22
 * description : html解析
 *
 * <AUTHOR> zhencai.cheng
 */
@Service
public class HtmlResolver extends AbstractResolver {
    @Override
    public String getKey() {
        return "html";
    }

    @Override
    public void parse(File file, Resume resume) throws Exception {
    }

    @Override
    public void resolve(File file, Resume resume) throws Exception {

    }
}
