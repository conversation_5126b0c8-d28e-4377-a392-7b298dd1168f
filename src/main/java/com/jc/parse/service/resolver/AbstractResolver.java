package com.jc.parse.service.resolver;

import com.alibaba.fastjson2.JSONObject;
import com.jc.base.exception.BusinessException;
import com.jc.base.exception.HttpException;
import com.jc.base.kits.CollectionKit;
import com.jc.base.kits.DateKit;
import com.jc.base.kits.HttpKit;
import com.jc.base.kits.MapKit;
import com.jc.parse.bean.Resume;
import com.jc.parse.constants.Constant;
import com.jc.parse.constants.Patterned;
import com.jc.parse.constants.Talent;
import com.jc.parse.kits.ParseKit;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanWrapper;
import org.springframework.beans.BeanWrapperImpl;

import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * date : 2024/10/22
 * description : 解析器抽象类
 *
 * <AUTHOR> zhencai.cheng
 */
public abstract class AbstractResolver implements Resolver {

    private String getBase(String content) throws HttpException {
        String result = HttpKit.postBody(Constant.Api.PARSE_URL,
                MapKit.of("model", "llama3-chinese",
                        "stream", false,
                        "prompt", "提取出姓名、性别、手机号" + content)
        );
        JSONObject response = JSONObject.parseObject(result);
        return response.getString("response");
    }

    protected String apiRequest(File file) throws BusinessException {
        throw new BusinessException("api调用已关闭成本较高！------");
    }

    /**
     * 解析简历内容
     *
     * @param content 简历内容
     * @return 简历对象
     */
    protected Resume standard(String content, Resume resume) throws HttpException {
        String mobile = ParseKit.match(content, Patterned.mobile.getDoc());
        if (StringUtils.isNotBlank(mobile) && mobile.contains("-")) {
            content = content.replaceAll(mobile, mobile.replaceAll("-", ""));
            mobile = mobile.replaceAll("-", "");
        }
        String[] split = this.split(content);
        List<Integer> typeList = new ArrayList<>();
        Map<Integer, List<String>> resumeMap = MapKit.newMap(Talent.ITEM_MAP.size());
        Talent talent = Talent.unknown;
        for (String s : split) {
            if (StringUtils.isBlank(s)) {
                continue;
            }
            for (Map.Entry<Integer, Talent> entry : Talent.ITEM_MAP.entrySet()) {
                if (!typeList.contains(entry.getKey())
                        && pattern(s, entry.getValue().getPattern())) {
                    typeList.add(entry.getKey());
                    talent = entry.getValue();
                    break;
                }
            }
            List<String> list = resumeMap.get(talent.getValue());
            if (Objects.isNull(list)) {
                list = new ArrayList<>();
                resumeMap.put(talent.getValue(), list);
            }
            list.add(s);
        }

        resume.setMobile(mobile);
        BeanWrapper wrapper = new BeanWrapperImpl(resume);
        for (Map.Entry<Integer, Talent> entry : Talent.ITEM_MAP.entrySet()) {
            if (StringUtils.isBlank(entry.getValue().getField())) {
                continue;
            }
            List<String> list = resumeMap.get(entry.getKey());
            if (CollectionKit.isNotEmpty(list)) {
                wrapper.setPropertyValue(entry.getValue().getField(), CollectionKit.join(list, "\n"));
            }

        }
        List<String> list = resumeMap.get(Talent.unknown.getValue());
        if (CollectionKit.isNotEmpty(list) && StringUtils.isBlank(resume.getUserName())) {
            this.parseBase(list, resume);
        }

//        List<String> personal = resumeMap.get(Talent.personal_info.getValue());

        //TODO 对个人信息处理 personal
        return resume;
    }

    private void parseBase(List<String> list, Resume resume) throws HttpException {
        if (CollectionKit.isEmpty(list)) {
            return;
        }
        String content = CollectionKit.join(list, "\n");
        if (!content.contains(resume.getMobile())) {
            return;
        }
        // 姓名
        String temp = list.getFirst();
        if (StringUtils.isNotBlank(temp)
                && resume.getFileName().contains(temp)
                && temp.length() < 5) {
            resume.setUserName(temp);
        }
        //性别
        temp = ParseKit.match(content, Patterned.sex.getDoc());
        if (StringUtils.isNotBlank(temp)) {
            resume.setSex(temp);
        }
        //年龄
        temp = ParseKit.match(content, Patterned.age.getDoc(), 1);
        if (StringUtils.isNotBlank(temp)) {
            temp = temp.replace("岁", "");
            resume.setBirthday(DateKit.addYears(-Integer.parseInt(temp)));
        }
        temp = ParseKit.match(content, Patterned.email.getDoc());
        if (StringUtils.isNotBlank(temp)) {
            resume.setEmail(temp);
        }
        temp = ParseKit.match(content, Patterned.workYear.getDoc(), 1);
        if (StringUtils.isNotBlank(temp)) {
            resume.setWorkYear(temp);
        }
        temp = ParseKit.match(content, Patterned.expect.getDoc(), 1);
        if (StringUtils.isNotBlank(temp)) {
            resume.setExpectJob(temp);
        }
        temp = ParseKit.match(content, Patterned.salary.getDoc(), 1);
        if (StringUtils.isNotBlank(temp)) {
            resume.setExpectSalary(temp);
        }

        if (StringUtils.isBlank(resume.getUserName())) {
            this.llamaBase(content, resume);
        }
    }

    private void llamaBase(String content, Resume resume) throws HttpException {
        String base = this.getBase(content);
        String temp;
        if (StringUtils.isBlank(resume.getUserName())) {
            temp = ParseKit.match(base, Patterned.name.getLlama(), 1);
            resume.setUserName(temp);
        }
        if (StringUtils.isBlank(resume.getSex())) {
            temp = ParseKit.match(base, Patterned.sex.getLlama(), 1);
            resume.setSex(temp);
        }
        if (Objects.isNull(resume.getBirthday())) {
            temp = ParseKit.match(base, Patterned.age.getLlama(), 1);
            if (StringUtils.isNotBlank(temp)) {
                temp = temp.replace("岁", "");
                resume.setBirthday(DateKit.addYears(-Integer.parseInt(temp)));
            }
        }
        if (StringUtils.isBlank(resume.getMobile())) {
            temp = ParseKit.match(base, Patterned.mobile.getLlama(), 1);
            resume.setMobile(temp);
        }
    }


    /**
     * 匹配
     *
     * @param content 内容
     * @param pattern 匹配规则
     * @return 是否匹配
     */
    private boolean pattern(String content, String... pattern) {
        for (String item : pattern) {
            if (content.contains(item) && content.indexOf(item) <= 2) {
                return true;
            }
        }
        return false;
    }

    /**
     * 分割
     *
     * @param content 内容
     * @return 分割后的数组
     */
    private String[] split(String content) {
        if (StringUtils.isBlank(content)) {
            return new String[]{};
        }
        if (content.contains("\n\t")) {
            return content.split("\n\t");
        } else if (content.contains("\n")) {
            return content.split("\n");
        } else if (content.contains("\t")) {
            return content.split("\t");
        } else if (content.contains(" ")) {
            return content.split(" ");
        } else if (content.contains(",")) {
            return content.split(",");
        }
        return new String[]{content};
    }


}
