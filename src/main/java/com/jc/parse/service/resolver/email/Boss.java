package com.jc.parse.service.resolver.email;

import com.jc.base.kits.DateKit;
import com.jc.parse.bean.Resume;
import com.jc.parse.bean.bo.EmailContent;
import com.jc.parse.constants.Channel;
import org.apache.commons.lang3.StringUtils;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * date : 2024/11/2
 * description :
 *
 * <AUTHOR> zhencai.cheng
 */
@Service
public class Boss implements ChannelResolver {
    @Override
    public Channel getKey() {
        return Channel.BOSS;
    }

    @Override
    public void parse(EmailContent content, Resume resume) throws Exception {
        this.getText(content.getHtml(), resume);
        if (StringUtils.isBlank(resume.getUserName())) {
            String[] temp = content.getSubject().split("\\|");
            resume.setUserName(temp[0]);
            if (StringUtils.isNotBlank(resume.getUserName())
                    && StringUtils.isBlank(resume.getSex())) {
                if (resume.getUserName().contains("先生")) {
                    resume.setSex("男");
                } else if (resume.getUserName().contains("女士")) {
                    resume.setSex("女");
                }
            }
        }
    }

    private void getText(String html, Resume resume) {
        Document document = Jsoup.parse(html);
        Elements elements = document.select("colgroup");
        if (elements.isEmpty()) {
            return;
        }
        Element element = elements.first();
        if (Objects.isNull(element)) {
            return;
        }
        element = element.parent();
        if (Objects.isNull(element)) {
            return;
        }
        // 获取基本信息
        elements = element.select("td");
        setBase(elements.getFirst(), resume);
    }

    /**
     * 设置简历基本信息
     *
     * @param element 元素
     * @param resume  简历
     */
    private void setBase(Element element, Resume resume) {
        Elements elements = element.select("span");
        resume.setUserName(elements.get(0).text());
        resume.setSex(elements.get(1).text());
        String temp = elements.get(2).text().replace("岁", "");
        resume.setBirthday(DateKit.addYears(-Integer.parseInt(temp)));
        resume.setEducation(elements.get(4).text());
        resume.setWorkYear(elements.get(5).text().replace("年", ""));

    }
}
