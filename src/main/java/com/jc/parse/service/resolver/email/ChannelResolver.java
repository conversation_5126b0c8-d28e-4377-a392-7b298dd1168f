package com.jc.parse.service.resolver.email;

import com.jc.parse.bean.Resume;
import com.jc.parse.bean.bo.EmailContent;
import com.jc.parse.constants.Channel;
import com.jc.parse.service.resolver.ResolverFactory;
import org.springframework.beans.factory.InitializingBean;

/**
 * date : 2024/11/1
 * description : 各个渠道邮件解析器
 *
 * <AUTHOR> zhencai.cheng
 */
public interface ChannelResolver extends InitializingBean {

    /**
     * 获取简历解析器标识
     *
     * @return 简历解析器标识
     */
    Channel getKey();

    /**
     * 解析简历
     *
     * @param content 邮件内容
     * @return 简历
     * @throws Exception 异常
     */
    void parse(EmailContent content, Resume resume) throws Exception;

    @Override
    default void afterPropertiesSet() throws Exception {
        ResolverFactory.registerEmail(getKey(), this);
    }
}
