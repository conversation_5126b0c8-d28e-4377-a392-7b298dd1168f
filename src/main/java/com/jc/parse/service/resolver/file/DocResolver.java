package com.jc.parse.service.resolver.file;

import com.jc.parse.bean.Resume;
import com.jc.parse.service.resolver.AbstractResolver;
import org.apache.poi.hwpf.HWPFDocument;
import org.apache.poi.hwpf.extractor.WordExtractor;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.FileInputStream;

/**
 * date : 2024/10/22
 * description : 2003 word解析
 *
 * <AUTHOR> zhencai.cheng
 */
@Service
public class DocResolver extends AbstractResolver {
    @Override
    public String getKey() {
        return "doc";
    }

    @Override
    public void parse(File file, Resume resume) throws Exception {
        String content = apiRequest(file);
        super.standard(content, resume);
    }

    @Override
    public void resolve(File file, Resume resume) throws Exception {
        FileInputStream fis = new FileInputStream(file);
        HWPFDocument document = new HWPFDocument(fis);
        WordExtractor extractor = new WordExtractor(document);
        String content = extractor.getText();
        fis.close();
        super.standard(content, resume);
    }
}
