package com.jc.parse.service;

import com.alibaba.fastjson2.JSONObject;
import com.jc.base.exception.BusinessException;
import com.jc.base.kits.CollectionKit;
import com.jc.base.kits.MapKit;
import com.jc.base.kits.RequestKit;
import com.jc.base.persistence.redis.RedisCache;
import com.jc.base.persistence.redis.RedisQueue;
import com.jc.parse.bean.Resume;
import com.jc.parse.bean.bo.EmailAccount;
import com.jc.parse.bean.bo.EmailProtocol;
import com.jc.parse.constants.Channel;
import com.jc.parse.constants.Constant;
import com.jc.parse.kits.EmailKit;
import com.jc.parse.persistence.feign.support.AccountSupport;
import com.jc.parse.persistence.feign.support.MessageSupport;
import com.sun.mail.imap.IMAPStore;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import javax.mail.*;
import javax.mail.search.ComparisonTerm;
import javax.mail.search.ReceivedDateTerm;
import java.time.Duration;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * date : 2024/10/17
 * description : 邮箱附件采集
 *
 * <AUTHOR> zhencai.cheng
 */
@Service
public class EmailService {

    private final Logger logger = LoggerFactory.getLogger(EmailService.class);
    @Value("${parse.resume.path}")
    private String resumePath;
    @Resource
    private AccountSupport accountSupport;
    @Resource
    private RedisQueue redisQueue;
    @Resource
    private RedisCache redisCache;
    @Resource
    private MessageSupport messageSupport;
    @Resource
    private ThreadPoolTaskExecutor taskExecutor;

    @Resource
    private ResumeService resumeService;
    @Resource
    private EmailSyncTimeService emailSyncTimeService;

    /**
     * 获取邮件附件
     *
     * @return json
     */
    public JSONObject parseEmail() {

        String totalKey = Constant.EMAIL_PARSE_LOCK + RequestKit.getIp();
        boolean exist = redisCache.setIfAbsent(totalKey, "1", Duration.of(5, ChronoUnit.MINUTES));
        if (!exist) {
            logger.warn("------不用重复请求采集：{}", RequestKit.getIp());
            return MapKit.of();
        }
        List<EmailProtocol> emailProtocols = accountSupport.getEmailList(
                //邮件正常
                MapKit.of("emailState", 1,
                        //账号状态正常
                        "accountState", 1,
                        //站源 智联、boss
                        "channelTypeList", CollectionKit.ofArray(2, 3)));
        for (EmailProtocol emailProtocol : emailProtocols) {
            List<EmailAccount> accounts = emailProtocol.getEmailList();
            for (EmailAccount emailAccount : accounts) {
                String branchKey = Constant.EMAIL_PARSE_BRANCH_LOCK + emailAccount.getEmail();
                boolean handler = redisCache.setIfAbsent(branchKey, "1", Duration.of(10, ChronoUnit.MINUTES));
                if (!handler) {
                    logger.warn("------{}正在采集，请稍后重试", emailAccount.getEmail());
                    continue;
                }
                try {
                    Session session = createSession(emailProtocol);
                    parseEmail(session, emailAccount, emailProtocol);
                } catch (Exception e) {
                    logger.error("{}获取邮件附件异常", emailAccount.getEmail(), e);
                }
                redisCache.delete(branchKey);
            }
        }
        return null;
    }

    /**
     * 创建session
     *
     * @param emailProtocol 邮箱
     * @return session
     * @throws BusinessException 业务异常
     */
    Session createSession(EmailProtocol emailProtocol) throws BusinessException {
        List<EmailAccount> accounts = emailProtocol.getEmailList();
        if (CollectionKit.isEmpty(accounts)) {
            return null;
        }
        Properties properties = new Properties();
        if ("imaps".equalsIgnoreCase(emailProtocol.getProtocol()) || "imap".equalsIgnoreCase(emailProtocol.getProtocol())) {
            properties.setProperty("mail.store.protocol", emailProtocol.getProtocol());
            properties.setProperty("mail.imap.host", emailProtocol.getHost());
            properties.setProperty("mail.imap.port", emailProtocol.getPort());
            properties.setProperty("mail.imap.ssl.enable", emailProtocol.getEnableSsl());
        } else if ("pop3".equalsIgnoreCase(emailProtocol.getProtocol())) {
            properties.setProperty("mail.store.protocol", emailProtocol.getProtocol());
            properties.setProperty("mail.pop3.host", emailProtocol.getHost());
            properties.setProperty("mail.pop3.port", emailProtocol.getPort());
            properties.setProperty("mail.pop3.ssl.enable", emailProtocol.getEnableSsl());
        } else {
            throw new BusinessException(emailProtocol.getProtocol() + "暂不支持该协议");
        }
        return Session.getDefaultInstance(properties);
    }

    /**
     * 获取邮件附件
     *
     * @param session       session
     * @param account       账号
     * @param emailProtocol 邮箱
     */
    void parseEmail(Session session, EmailAccount account, EmailProtocol emailProtocol) {
        // 检查是否应该跳过同步（失败次数过多）
        if (emailSyncTimeService.shouldSkipSync(account.getEmail())) {
            logger.warn("{}:同步失败次数过多，跳过本次同步", account.getEmail());
            return;
        }

        // 记录本次同步时间，用于统一时间计算
        long syncStartTime = System.currentTimeMillis();

        // 是否有新简历
        boolean hasNewResume = false;

        try {
            Store store = session.getStore(emailProtocol.getProtocol());
            store.connect(emailProtocol.getHost(), account.getEmail(), account.getEmailPassword());
            logger.info("------{}邮件登录成功", account.getEmail());

            //网易邮箱需要设置id
            if (account.getEmail().contains("@163.com")) {
                IMAPStore imapStore = (IMAPStore) store;
                imapStore.id(MapKit.ofMap("name", "123456", "version", "1.0", "os", "mac", "os.version", "12.6.8", "vendor", "mac", "contact", "<EMAIL>"));
            }

            //获取邮箱中的邮件文件
            Folder inbox = store.getFolder("INBOX");
            inbox.open(Folder.READ_ONLY);

            // 根据同步规则获取邮件
            Message[] messages = getMessagesBySyncRule(inbox, account.getEmail(), syncStartTime);
            logger.info("------{}邮件共{}封待处理邮件", account.getEmail(), messages.length);

            for (Message message : messages) {
                //判断哪个渠道发送的邮件
                Channel channel = EmailKit.getChannel(message);
                if (Objects.isNull(channel) || Objects.equals(channel, Channel.UNKNOWN)) {
                    continue;
                }
                String title = message.getSubject();
                if (Objects.equals(channel, Channel.BOSS)
                        && !title.contains("|")) {
                    //boss邮件标题，不包含|直接跳过
                    continue;
                }
                Resume resume = new Resume();
                resume.setReceivedDate(message.getReceivedDate());
                EmailKit.parseMessage(message, resume, channel, resumePath);
                if (StringUtils.isNotBlank(resume.getUserName())) {
                    logger.info("简历信息：{}", resume);
                    resume.setResumeOwner(account.getAccountOwner());
                    resumeService.insertResume(resume);
                    redisQueue.send(Constant.Queue.STANDARD_RESUME_QUEUE, resume.getResumeId().toString());
                    hasNewResume = true;
                }
                message.setFlag(Flags.Flag.SEEN, true);
            }
            inbox.close(true);
            store.close();

            // 同步成功，记录本次同步开始时间作为下次同步的起始点
            String lastSyncKey = Constant.Cache.EMAIL_LAST_SYNC + account.getEmail();
            redisCache.set(lastSyncKey, String.valueOf(syncStartTime));

            // 清除同步失败记录
            emailSyncTimeService.clearSyncFailure(account.getEmail());
            // 标记首次同步完成
            if (emailSyncTimeService.isFirstSync(account.getEmail())) {
                emailSyncTimeService.markFirstSyncComplete(account.getEmail());
            }

            // 处理7天无数据同步检测
            boolean finalHasNewResume = hasNewResume;
            taskExecutor.execute(() -> handleNoDataSyncCheck(account, finalHasNewResume));
        } catch (AuthenticationFailedException e) {
            // 账号密码错误
            logger.error("{}:邮箱账号或密码错误", account.getEmail(), e);
            taskExecutor.execute(() -> handleAuthenticationError(account));
        } catch (Exception e) {
            // 记录同步失败
            emailSyncTimeService.recordSyncFailure(account.getEmail());
            logger.error("{}:获取邮件异常", account.getEmail(), e);
            //如果邮件长时间链接断开，重新链接
//            if (e instanceof FolderClosedException) {
//                this.parseEmail(session, account, emailProtocol);
//                Long times = redisCache.increment(Constant.Cache.COLLECT_EMAIL_TIMES + account.getEmail(), Duration.of(30, ChronoUnit.MINUTES));
//                //尝试2次之后退出
//                if (times > 2) {
//                    return;
//                }
//            }
//            logger.error("{}:获取邮件附件异常", account.getEmail(), e);
        }
    }

    /**
     * 手动获取邮件附件
     *
     * @param email 邮箱
     */
    public void collectEmail(String email) throws BusinessException {
        JSONObject emailMap = accountSupport.getEmail(email);
        logger.info("手动获取邮件附件:{}", emailMap);
        EmailAccount account = new EmailAccount();
        account.setEmail(email);
        account.setEmailPassword(emailMap.getString("emailPassword"));
        account.setAccountOwner(emailMap.getLong("accountOwner"));

        EmailProtocol e = new EmailProtocol();
        e.setHost(emailMap.getString("host"));
        e.setPort(emailMap.getString("port"));
        e.setEnableSsl(emailMap.getString("enableSsl"));
        e.setProtocol(emailMap.getString("protocol"));
        e.setEmailList(CollectionKit.ofList(account));

        Session session = createSession(e);
        parseEmail(session, account, e);
    }
    
    /**
     * 根据同步规则获取邮件
     *
     * @param inbox 收件箱
     * @param email 邮箱地址
     * @param syncStartTime 本次同步开始时间
     * @return 邮件数组
     */
    private Message[] getMessagesBySyncRule(Folder inbox, String email, long syncStartTime) throws MessagingException {
        if (emailSyncTimeService.isFirstSync(email)) {
            // 首次同步：近30天内邮箱收到的简历，不区分已读/未读状态
            return getMessagesForFirstSync(inbox);
        } else {
            // 后续同步：按照简历接收时间段进行同步
            return getMessagesForTimeRangeSync(inbox, email, syncStartTime);
        }
    }
    
    /**
     * 获取首次同步的邮件（近30天内，不区分已读/未读）
     *
     * @param inbox 收件箱
     * @return 邮件数组
     */
    private Message[] getMessagesForFirstSync(Folder inbox) throws MessagingException {
        long thirtyDaysAgo = emailSyncTimeService.getThirtyDaysAgoTime();
        Date thirtyDaysAgoDate = new Date(thirtyDaysAgo);
        
        // 搜索近30天内收到的邮件
        ReceivedDateTerm receivedDateTerm = new ReceivedDateTerm(ComparisonTerm.GE, thirtyDaysAgoDate);
        Message[] messages = inbox.search(receivedDateTerm);
        
        logger.info("首次同步：获取近30天内邮件，共{}封", messages.length);
        return messages;
    }
    
    /**
     * 获取时间段同步的邮件
     *
     * @param inbox 收件箱
     * @param email 邮箱地址
     * @param syncStartTime 本次同步开始时间
     * @return 邮件数组
     */
    private Message[] getMessagesForTimeRangeSync(Folder inbox, String email, long syncStartTime) throws MessagingException {
        String lastSuccessKey = Constant.Cache.EMAIL_LAST_SYNC + email;
        String lastSuccessTimeStr = redisCache.get(lastSuccessKey);

        long startTime;
        if (Objects.isNull(lastSuccessTimeStr)) {
            startTime = syncStartTime - TimeUnit.HOURS.toMillis(1);
        } else {
            startTime = Long.parseLong(lastSuccessTimeStr);
        }

        Date startDate = new Date(startTime);
        ReceivedDateTerm startDateTerm = new ReceivedDateTerm(ComparisonTerm.GE, startDate);
        Message[] messages = inbox.search(startDateTerm);

        // 这儿可能是不支持时分秒的开始时间，只能到天，所以需要再次过滤时间断内的邮件
        List<Message> messageList = new ArrayList<>();
        for (Message message : messages) {
            Date receivedDate = message.getReceivedDate();
            if (Objects.nonNull(receivedDate)
                    && receivedDate.getTime() >= startTime
                    && receivedDate.getTime() < syncStartTime) {
                messageList.add(message);
            }
        }

        logger.info("时间段同步：获取{}之后的邮件，共{}封", startDate, messageList.size());
        return messageList.toArray(new Message[0]);
    }

    /**
     * 认证错误
     *
     * @param account 账号
     */
    private void handleAuthenticationError(EmailAccount account) {
        com.jc.parse.constants.Message.PopupMessage popupMessage = com.jc.parse.constants.Message.PopupMessage.CANNOT_READ_RESUME;
        this.messageSupport.addPopupMessage(account.getAccountOwner(),
                popupMessage.getTitle(),
                popupMessage.getContent(),
                popupMessage.getMessageType()
        );
    }

    /**
     * 处理7天无数据同步检测
     *
     * @param account     账号
     * @param hasNewResume 是否有新简历
     */
    private void handleNoDataSyncCheck(EmailAccount account, boolean hasNewResume) {
        String lastSyncKey = Constant.Cache.EMAIL_LAST_SYNC + account.getEmail();
        String noDataAlertKey = Constant.Cache.EMAIL_NO_DATA_ALERT + account.getEmail();

        if (hasNewResume) {
            // 有新简历，更新最后同步时间
            redisCache.set(lastSyncKey, String.valueOf(System.currentTimeMillis()), Duration.of(90, ChronoUnit.DAYS));
            // 清除无数据告警标记
            redisCache.delete(noDataAlertKey);
            logger.info("{}:更新最后同步时间", account.getEmail());
        } else {
            // 没有新简历，检查是否超过7天
            String lastSyncTimeStr = redisCache.get(lastSyncKey);
            if (StringUtils.isNotBlank(lastSyncTimeStr)) {
                long lastSyncTime = Long.parseLong(lastSyncTimeStr);
                long currentTime = System.currentTimeMillis();
                long daysDiff = (currentTime - lastSyncTime) / (1000 * 60 * 60 * 24);

                if (daysDiff >= 7) {
                    // 检查今天是否已经发送过告警
                    // 当前天数
                    String today = String.valueOf(System.currentTimeMillis() / (1000 * 60 * 60 * 24));
                    String alertSent = redisCache.get(noDataAlertKey);

                    if (!Objects.equals(today, alertSent)) {
                        // 发送7天无数据同步告警
                        handleNoDataSyncAlert(account);
                        // 标记今天已发送告警
                        redisCache.set(noDataAlertKey, today, Duration.of(2, ChronoUnit.DAYS));
                        logger.warn("{}:超过{}天无简历同步，已发送告警", account.getEmail(), daysDiff);
                    }
                }
            } else {
                // 首次同步，记录当前时间
                redisCache.set(lastSyncKey, String.valueOf(System.currentTimeMillis()), Duration.of(90, ChronoUnit.DAYS));
                logger.info("{}:首次同步，记录同步时间", account.getEmail());
            }
        }
    }

    /**
     * 处理无数据同步告警
     *
     * @param account 账号
     */
    private void handleNoDataSyncAlert(EmailAccount account) {
        com.jc.parse.constants.Message.PopupMessage popupMessage = com.jc.parse.constants.Message.PopupMessage.EXCEED_7_DAYS_NO_RESUME_SYNC;
        this.messageSupport.addPopupMessage(account.getAccountOwner(),
                popupMessage.getTitle(),
                String.format(popupMessage.getContent(), account.getEmail()),
                popupMessage.getMessageType()
        );
    }

}
