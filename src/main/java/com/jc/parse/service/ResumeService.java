package com.jc.parse.service;

import com.jc.base.kits.OperatorKit;
import com.jc.parse.bean.Resume;
import com.jc.parse.persistence.dao.ResumeDao;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * date : 2025-02-18 15:39:43
 * description : 爬虫简历(Resume)表服务实现类
 *
 * <AUTHOR>
 */
@Service
public class ResumeService {

    private final ResumeDao resumeDao;

    public ResumeService(ResumeDao resumeDao) {
        this.resumeDao = resumeDao;
    }

    /**
     * 新增数据
     *
     * @param resume 实例对象
     */
    public void insertResume(Resume resume) {
        OperatorKit.setCreate(resume);
        if (StringUtils.isBlank(resume.getMobile())) {
            resume.setConfirmState(2);
        } else {
            resume.setConfirmState(1);
        }
        this.resumeDao.insertResume(resume);
    }

}
