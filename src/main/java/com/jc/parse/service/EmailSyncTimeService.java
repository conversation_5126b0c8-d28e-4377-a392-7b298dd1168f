package com.jc.parse.service;

import com.jc.base.kits.DateKit;
import com.jc.base.persistence.redis.RedisCache;
import com.jc.parse.constants.Constant;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Objects;

/**
 * 邮箱同步时间管理服务
 *
 * <AUTHOR>
 */
@Service
public class EmailSyncTimeService {

    private final Logger logger = LoggerFactory.getLogger(EmailSyncTimeService.class);

    // 失败次数
    private final static long FAIL_COUNT = 2;

    @Resource
    private RedisCache redisCache;

    /**
     * 检查是否为首次同步
     *
     * @param email 邮箱地址
     * @return 是否为首次同步
     */
    public boolean isFirstSync(String email) {
        String firstSyncKey = Constant.Cache.EMAIL_FIRST_SYNC + email;
        String firstSync = redisCache.get(firstSyncKey);
        return Objects.isNull(firstSync);
    }

    /**
     * 标记首次同步完成
     *
     * @param email 邮箱地址
     */
    public void markFirstSyncComplete(String email) {
        String firstSyncKey = Constant.Cache.EMAIL_FIRST_SYNC + email;
        redisCache.set(firstSyncKey, "1");
        logger.info("{}:标记首次同步完成", email);
    }

    /**
     * 记录同步失败
     *
     * @param email 邮箱地址
     */
    public void recordSyncFailure(String email) {
        String failCountKey = Constant.Cache.EMAIL_SYNC_FAIL_COUNT + email;
        long failCount = redisCache.increment(failCountKey);
        logger.warn("{}:同步失败，当前失败次数: {}", email, failCount);
    }

    /**
     * 清除同步失败记录
     *
     * @param email 邮箱地址
     */
    public void clearSyncFailure(String email) {
        String failCountKey = Constant.Cache.EMAIL_SYNC_FAIL_COUNT + email;
        redisCache.delete(failCountKey);
        logger.info("{}:清除同步失败记录", email);
    }

    /**
     * 检查是否应该跳过同步（失败次数过多）
     *
     * @param email 邮箱地址
     * @return 是否应该跳过同步
     */
    public boolean shouldSkipSync(String email) {
        String failCountKey = Constant.Cache.EMAIL_SYNC_FAIL_COUNT + email;
        Long failCount = redisCache.get(failCountKey, Long.class);
        return Objects.nonNull(failCount) && failCount >= FAIL_COUNT;
    }

    /**
     * 获取30天前的时间戳
     *
     * @return 30天前的时间戳
     */
    public long getThirtyDaysAgoTime() {
        return DateKit.addDays(-30).getTime();
    }
    
    /**
     * 设置邮箱为非首次同步状态
     *
     * @param email 邮箱地址
     */
    public void setNonFirstSync(String email) {
        String firstSyncKey = Constant.Cache.EMAIL_FIRST_SYNC + email;
        redisCache.set(firstSyncKey, "1");
        logger.info("{}:设置为非首次同步状态", email);
    }
    
    /**
     * 设置邮箱上次成功同步时间
     *
     * @param email 邮箱地址
     * @param lastSyncTime 上次成功同步时间戳
     */
    public void setLastSyncTime(String email, long lastSyncTime) {
        String lastSyncKey = Constant.Cache.EMAIL_LAST_SYNC + email;
        redisCache.set(lastSyncKey, String.valueOf(lastSyncTime));
        logger.info("{}:设置上次成功同步时间为 {}", email, new Date(lastSyncTime));
    }

} 