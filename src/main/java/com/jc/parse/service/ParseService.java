package com.jc.parse.service;

import com.alibaba.fastjson2.JSONObject;
import com.jc.base.bean.BaseAccount;
import com.jc.base.exception.BusinessException;
import com.jc.base.kits.CollectionKit;
import com.jc.parse.bean.Resume;
import com.jc.parse.persistence.feign.support.AccountSupport;
import com.jc.parse.service.resolver.ResolverFactory;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Objects;

/**
 * date : 2024/10/22
 * description :
 *
 * <AUTHOR> zhencai.cheng
 */
@Service
public class ParseService {

    @Resource
    private AccountSupport accountSupport;
    private static final List<String> ATTACHMENT_SUFFIX = CollectionKit.ofList("jpg", "jpeg", "png");

    public Resume parse(MultipartFile file, JSONObject requestMap) throws Exception {
        if (Objects.isNull(file)) {
            throw new BusinessException("文件为空");
        }
        String originalFilename = file.getOriginalFilename();
        String suffix;
        if (StringUtils.isNotBlank(originalFilename)) {
            suffix = originalFilename.substring(originalFilename.lastIndexOf(".") + 1);
        } else {
            throw new BusinessException("文件名不存在");
        }
        if (ATTACHMENT_SUFFIX.contains(suffix)) {
            suffix = "image";
        }
        Resume resume = new Resume();
        ResolverFactory.getParseService(suffix).resolve(file.getResource().getFile(), resume);
        BaseAccount account = accountSupport.getAccount(requestMap.getString("email"));
        resume.setResumeOwner(account.getAccountId());
        return resume;
    }

}
