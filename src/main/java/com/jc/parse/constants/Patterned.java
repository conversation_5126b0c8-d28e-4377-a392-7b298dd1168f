package com.jc.parse.constants;

import java.util.regex.Pattern;

/**
 * date : 2024/11/6
 * description :
 *
 * <AUTHOR> zhencai.cheng
 */
public enum Patterned {
    name(
            Pattern.compile("(\\p{<PERSON><PERSON><PERSON>}+)"),
            Pattern.compile("(\\p{<PERSON><PERSON><PERSON>}+)"),
            Pattern.compile("姓名：(\\p{IsHan}+)")),
    mobile(
            Pattern.compile("(\\b\\d{11}\\b|\\d{3}-\\d{4}-\\d{4})"),
            Pattern.compile("(\\b\\d{11}\\b|\\d{3}-\\d{4}-\\d{4})"),
            Pattern.compile("手机号：\\d{11}")),
    sex(
            Pattern.compile("(男|女)"),
            Pattern.compile("(男|女)"),
            Pattern.compile("性别：(男|女)")),
    age(
            Pattern.compile("(\\d{2}|\\d{3})岁"),
            Pattern.compile("(\\d{4})年(\\d+)月"),
            Pattern.compile("年龄：(\\d{2}|\\d{3})岁")),
    email(
            Pattern.compile("[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}"),
            Pattern.compile("[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}"),
            Pattern.compile("[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}")
    ),
    expect(
            Pattern.compile("求职意向:(\\p{IsHan}+)"),
            Pattern.compile("求职意向:(\\p{IsHan}+)"),
            Pattern.compile("求职意向:(\\p{IsHan}+)")
    ),
    salary(
            Pattern.compile("期望薪资:([^|]+)"),
            Pattern.compile("期望薪资:([^|]+)"),
            Pattern.compile("期望薪资:([^|]+)")
    ),
    workYear(
            Pattern.compile("(\\d+)年(工作经验|经验)"),
            Pattern.compile("工作(\\d+)年"),
            Pattern.compile("(\\d+)年(工作经验|经验)")
    ),
    jobStatus(
            Pattern.compile("( 在职| 离职)"),
            Pattern.compile("( 在职| 离职)"),
            Pattern.compile("( 在职| 离职)")
    ),
    education(
            Pattern.compile("(高中|中专|大专|本科|硕士|博士)"),
            Pattern.compile("(高中|中专|大专|本科|硕士|博士)"),
            Pattern.compile("(高中|中专|大专|本科|硕士|博士)")
    ),
    school(
            Pattern.compile("(\\p{IsHan}+大学|\\p{IsHan}+学院)"),
            Pattern.compile("(\\p{IsHan}+大学|\\p{IsHan}+学院)"),
            Pattern.compile("(\\p{IsHan}+大学|\\p{IsHan}+学院)")
    );

    public final Pattern doc;
    public final Pattern html;
    public final Pattern llama;

    Patterned(Pattern doc, Pattern html, Pattern llama) {
        this.doc = doc;
        this.html = html;
        this.llama = llama;
    }

    public Pattern getDoc() {
        return doc;
    }

    public Pattern getHtml() {
        return html;
    }

    public Pattern getLlama() {
        return llama;
    }
}
    