package com.jc.parse.constants;

/**
 * date : 2025/07/17
 * description :
 *
 * <AUTHOR> czs
 */
public interface Message {

    /**
     * 弹窗消息
     */
    enum PopupMessage {
        // 1-无法读取邮箱简历
        CANNOT_READ_RESUME(1, "网络端口信息确认", "系统识别到您配置端口之后有修改邮箱密码，导致无法从您的邮箱中读取简历，请及时处理。"),
        // 2-超过7天无简历同步
        EXCEED_7_DAYS_NO_RESUME_SYNC(2, "网络端口信息确认", "系统识别到您的%s端口已经7天没有简历同步到鲸才云，请确认该端口是否还在使用。");

        private final int messageType;
        private final String title;
        private final String content;

        PopupMessage(int messageType, String title, String content) {
            this.messageType = messageType;
            this.title = title;
            this.content = content;
        }

        public int getMessageType() {
            return messageType;
        }

        public String getTitle() {
            return title;
        }

        public String getContent() {
            return content;
        }
    }

}
