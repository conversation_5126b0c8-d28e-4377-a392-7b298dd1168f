package com.jc.parse.constants;

import com.jc.base.kits.CollectionKit;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * date : 2024/10/23
 * description : 人才纬度
 *
 * <AUTHOR> zhencai.cheng
 */
public enum Talent {
    personal_info(1, "", CollectionKit.ofArray("个人信息","基本信息","个人简历","姓名")),
    work_experience(2, "workExperience", CollectionKit.ofArray("工作经历")),
    project_experience(3, "projectExperience", CollectionKit.ofArray("项目经历", "项目经验", "项目简介")),
    education_experience(4, "educationExperience", CollectionKit.ofArray("教育经历", "教育背景", "培训经历")),
    intro(5, "intro", CollectionKit.ofArray("个人优势", "自我介绍", "自我评价")),
    skill(6, "skill", CollectionKit.ofArray("技能","个人技能")),
    award(7, "award", CollectionKit.ofArray("获奖情况")),
    other(8, "other", CollectionKit.ofArray("其他")),
    certificate(9, "certificate", CollectionKit.ofArray("其他")),
    unknown(null, "", CollectionKit.ofArray("男","女"));
    private final Integer value;

    private final String field;
    private final String[] pattern;
    public static final Map<Integer, Talent> ITEM_MAP = Arrays.stream(Talent.values())
            .collect(Collectors.toMap(Talent::getValue, item -> item));

    Talent(Integer value, String field, String[] pattern) {
        this.value = value;
        this.field = field;
        this.pattern = pattern;
    }

    public Integer getValue() {
        return value;
    }

    public String getField() {
        return field;
    }

    public String[] getPattern() {
        return pattern;
    }


}
