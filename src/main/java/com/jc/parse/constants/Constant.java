package com.jc.parse.constants;

import com.jc.base.constant.BaseConstants;

/**
 * date : 2024-05-29 12:22:26
 * description : 常量
 *
 * <AUTHOR>
 */
public interface Constant extends BaseConstants {

    String PROJECT_NAME = "spider-parse";
    String EMAIL_PARSE_LOCK = "email_parse_lock";
    String EMAIL_PARSE_BRANCH_LOCK = "email_parse_branch_lock";


    interface Cache extends BaseConstants.Cache {
        /**
         * open api cookie
         */
        String OPEN_API_COOKIE = "open_api_cookie";
        /**
         * open api cookie lock
         */
        String OPEN_API_COOKIE_LOCK = "open_api_cookie_lock";

        //采集邮箱
        String COLLECT_EMAIL = "collect_email";

        String COLLECT_EMAIL_TIMES = "collect_email_times:";
        
        /**
         * 邮箱最后同步时间
         */
        String EMAIL_LAST_SYNC = "email_last_sync:";
        
        /**
         * 邮箱无数据告警标记
         */
        String EMAIL_NO_DATA_ALERT = "email_no_data_alert:";
        
        /**
         * 邮箱首次同步标记
         */
        String EMAIL_FIRST_SYNC = "email_first_sync:";
        
        /**
         * 邮箱同步失败次数
         */
        String EMAIL_SYNC_FAIL_COUNT = "email_sync_fail_count:";
    }


    interface Api {
        /**
         * 登录地址
         */
        String LOGIN_URL = "/api/v1/auths/signin";
        /**
         * 解析地址
         */
        String FILE_URL = "/api/v1/files/";
        String PARSE_URL = "http://localhost:11434/api/generate";


    }

    interface Queue {

        String STANDARD_RESUME_QUEUE = "spider_standard_resume_queue";
        String RESUME_QUEUE = "spider_email_resume_queue";
    }


}

