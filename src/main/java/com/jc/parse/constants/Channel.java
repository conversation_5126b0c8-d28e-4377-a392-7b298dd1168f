package com.jc.parse.constants;

/**
 * date : 2024/11/1
 * description :
 *
 * <AUTHOR> zhencai.cheng
 */
public enum Channel {

    GAN_JI(1, "58", "@zp.58.com", true, 18),
    BOSS(2, "BOS<PERSON>", "zhipin", true, 21),
    Z<PERSON><PERSON>_PIN(3, "智联招聘", "zhaopin", true, 19),
    UNKNOWN(null, "未知", null, false, 0);
    private final Integer value;
    private final String display;

    private final String emailSuffix;
    private final Boolean text;
    private final Integer resumeSource;

    Channel(Integer value, String display, String emailSuffix, Boolean text, Integer resumeSource) {
        this.value = value;
        this.display = display;
        this.emailSuffix = emailSuffix;
        this.text = text;
        this.resumeSource = resumeSource;
    }

    public Integer getValue() {
        return value;
    }

    public String getDisplay() {
        return display;
    }

    public String getEmailSuffix() {
        return emailSuffix;
    }

    public Boolean getText() {
        return text;
    }

    public Integer getResumeSource() {
        return resumeSource;
    }
}
