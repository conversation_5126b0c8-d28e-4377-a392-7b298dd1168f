package com.jc.parse;

import com.jc.base.config.tomcat.TomcatConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.data.redis.RedisRepositoriesAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaAutoConfiguration;
import org.springframework.boot.web.servlet.ServletComponentScan;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Import;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.session.data.redis.config.annotation.web.http.EnableRedisHttpSession;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * date : 2024-05-29 12:22:26
 * description : 启动类
 *
 * <AUTHOR>
 */
@ServletComponentScan(basePackages = "com.jc")
@SpringBootApplication(scanBasePackages = "com.jc", exclude = {
        DataSourceAutoConfiguration.class,
        HibernateJpaAutoConfiguration.class,
        RedisRepositoriesAutoConfiguration.class
})
@Import({
        TomcatConfig.class
})
@EnableTransactionManagement(order = 1)
@EnableAsync
@EnableFeignClients(basePackages = "com.jc.parse.persistence.feign")
@EnableRedisHttpSession(maxInactiveIntervalInSeconds = 28800)
public class ParseApplication {

    private static final Logger logger = LoggerFactory.getLogger(ParseApplication.class);

    public static void main(String[] args) {
        try {
            SpringApplication.run(ParseApplication.class, args);
            logger.info("启动成功~~~~~~~~~~~~~~~~~~~~~~~~~~~");
        } catch (Exception e) {
            logger.error("启动失败！！！！！！！！！！！！！！");
            throw e;
        }
    }
}

