package com.jc.parse.bean;

import com.jc.base.bean.BaseBean;
import com.jc.validate.annotation.FieldNote;
import com.jc.validate.core.FieldEnum;
import com.jc.validate.support.Op;

import java.util.Date;

/**
 * date : 2025-03-03 15:07:22
 * description : 爬虫简历(SpiderResume)实体类
 *
 * <AUTHOR>
 */
public class Resume extends BaseBean {


    @FieldNote(name = "简历ID", type = FieldEnum.NUMBER, isNullAble = false, groups = {Op.update})
    private Long resumeId;

    @FieldNote(name = "简历确认状态 1-已确认 2-未确认", type = FieldEnum.NUMBER, isNullAble = false)
    private Integer confirmState;

    @FieldNote(name = "文件名", type = FieldEnum.STRING, isNullAble = false)
    private String fileName;

    @FieldNote(name = "姓名", type = FieldEnum.STRING)
    private String userName;

    @FieldNote(name = "出生日期", type = FieldEnum.DATE)
    private Date birthday;

    @FieldNote(name = "性别", type = FieldEnum.STRING)
    private String sex;

    @FieldNote(name = "手机号", type = FieldEnum.STRING)
    private String mobile;

    @FieldNote(name = "邮箱", type = FieldEnum.STRING)
    private String email;

    @FieldNote(name = "地址", type = FieldEnum.STRING)
    private String address;

    @FieldNote(name = "学历", type = FieldEnum.STRING)
    private String education;

    @FieldNote(name = "教育经历", type = FieldEnum.STRING)
    private String educationExperience;

    @FieldNote(name = "工作经历", type = FieldEnum.STRING)
    private String workExperience;

    @FieldNote(name = "项目经历", type = FieldEnum.STRING)
    private String projectExperience;

    @FieldNote(name = "技能", type = FieldEnum.STRING)
    private String skill;

    @FieldNote(name = "个人介绍", type = FieldEnum.STRING)
    private String intro;

    @FieldNote(name = "获奖情况", type = FieldEnum.STRING)
    private String award;

    @FieldNote(name = "证书", type = FieldEnum.STRING)
    private String certificate;

    @FieldNote(name = "工作状态", type = FieldEnum.STRING)
    private String jobStatus;

    @FieldNote(name = "期望薪资", type = FieldEnum.STRING)
    private String expectSalary;

    @FieldNote(name = "期望城市", type = FieldEnum.STRING)
    private String expectCity;

    @FieldNote(name = "期望职位", type = FieldEnum.STRING)
    private String expectJob;

    @FieldNote(name = "工作年限", type = FieldEnum.STRING)
    private String workYear;

    @FieldNote(name = "简历拥有者", type = FieldEnum.NUMBER, isNullAble = false)
    private Long resumeOwner;

    @FieldNote(name = "渠道来源", type = FieldEnum.NUMBER, isNullAble = false)
    private Integer resumeSource;

    @FieldNote(name = "是否同步 1是 2否", type = FieldEnum.NUMBER, isNullAble = false)
    private Integer enableSync;

    @FieldNote(name = "收到时间", type = FieldEnum.DATE)
    private Date receivedDate;

    private String other;

    private String subject;
    public Long getResumeId() {
        return resumeId;
    }

    public void setResumeId(Long resumeId) {
        this.resumeId = resumeId;
    }

    public Integer getConfirmState() {
        return confirmState;
    }

    public void setConfirmState(Integer confirmState) {
        this.confirmState = confirmState;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public Date getBirthday() {
        return birthday;
    }

    public void setBirthday(Date birthday) {
        this.birthday = birthday;
    }

    public String getSex() {
        return sex;
    }

    public void setSex(String sex) {
        this.sex = sex;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getEducation() {
        return education;
    }

    public void setEducation(String education) {
        this.education = education;
    }

    public String getEducationExperience() {
        return educationExperience;
    }

    public void setEducationExperience(String educationExperience) {
        this.educationExperience = educationExperience;
    }

    public String getWorkExperience() {
        return workExperience;
    }

    public void setWorkExperience(String workExperience) {
        this.workExperience = workExperience;
    }

    public String getProjectExperience() {
        return projectExperience;
    }

    public void setProjectExperience(String projectExperience) {
        this.projectExperience = projectExperience;
    }

    public String getSkill() {
        return skill;
    }

    public void setSkill(String skill) {
        this.skill = skill;
    }

    public String getIntro() {
        return intro;
    }

    public void setIntro(String intro) {
        this.intro = intro;
    }

    public String getAward() {
        return award;
    }

    public void setAward(String award) {
        this.award = award;
    }

    public String getCertificate() {
        return certificate;
    }

    public void setCertificate(String certificate) {
        this.certificate = certificate;
    }

    public String getJobStatus() {
        return jobStatus;
    }

    public void setJobStatus(String jobStatus) {
        this.jobStatus = jobStatus;
    }

    public String getExpectSalary() {
        return expectSalary;
    }

    public void setExpectSalary(String expectSalary) {
        this.expectSalary = expectSalary;
    }

    public String getExpectCity() {
        return expectCity;
    }

    public void setExpectCity(String expectCity) {
        this.expectCity = expectCity;
    }

    public String getExpectJob() {
        return expectJob;
    }

    public void setExpectJob(String expectJob) {
        this.expectJob = expectJob;
    }

    public String getWorkYear() {
        return workYear;
    }

    public void setWorkYear(String workYear) {
        this.workYear = workYear;
    }

    public Long getResumeOwner() {
        return resumeOwner;
    }

    public void setResumeOwner(Long resumeOwner) {
        this.resumeOwner = resumeOwner;
    }

    public Integer getResumeSource() {
        return resumeSource;
    }

    public void setResumeSource(Integer resumeSource) {
        this.resumeSource = resumeSource;
    }

    public Integer getEnableSync() {
        return enableSync;
    }

    public void setEnableSync(Integer enableSync) {
        this.enableSync = enableSync;
    }

    public Date getReceivedDate() {
        return receivedDate;
    }

    public void setReceivedDate(Date receivedDate) {
        this.receivedDate = receivedDate;
    }

    public String getSubject() {
        return subject;
    }

    public void setSubject(String subject) {
        this.subject = subject;
    }

    public String getOther() {
        return other;
    }

    public void setOther(String other) {
        this.other = other;
    }
}

