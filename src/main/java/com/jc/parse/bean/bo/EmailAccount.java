package com.jc.parse.bean.bo;

import com.jc.base.bean.HashBean;

/**
 * date : 2024/10/17
 * description : 邮箱账号信息
 *
 * <AUTHOR> zhencai.cheng
 */
public class EmailAccount extends HashBean {

    private String email;

    private String emailPassword;

    private Long accountId;

    private Long accountOwner;

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getEmailPassword() {
        return emailPassword;
    }

    public void setEmailPassword(String emailPassword) {
        this.emailPassword = emailPassword;
    }

    public Long getAccountId() {
        return accountId;
    }

    public void setAccountId(Long accountId) {
        this.accountId = accountId;
    }

    public Long getAccountOwner() {
        return accountOwner;
    }

    public void setAccountOwner(Long accountOwner) {
        this.accountOwner = accountOwner;
    }
}
