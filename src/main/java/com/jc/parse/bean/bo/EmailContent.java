package com.jc.parse.bean.bo;

import com.jc.base.bean.HashBean;

/**
 * date : 2024/11/1
 * description :
 *
 * <AUTHOR> zhencai.cheng
 */
public class EmailContent extends HashBean {

    /**
     * 邮件主题
     */
    private String subject;
    /**
     * 邮件内容
     */
    private String content;
    /**
     * 邮件内容html
     */
    private String html;

    public String getSubject() {
        return subject;
    }

    public void setSubject(String subject) {
        this.subject = subject;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getHtml() {
        return html;
    }

    public void setHtml(String html) {
        this.html = html;
    }
}
