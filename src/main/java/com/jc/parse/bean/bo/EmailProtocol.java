package com.jc.parse.bean.bo;

import com.jc.base.bean.HashBean;

import java.util.List;

/**
 * date : 2024/10/17
 * description : 邮箱服务及账号信息
 *
 * <AUTHOR> zhencai.cheng
 */
public class EmailProtocol extends HashBean {
    /**
     * 邮件服务器
     */
    private String host;
    /**
     * 端口
     */
    private String port;
    /**
     * 协议
     */
    private String protocol;

    private String enableSsl;

    private List<EmailAccount> emailList;

    public String getHost() {
        return host;
    }

    public void setHost(String host) {
        this.host = host;
    }

    public String getPort() {
        return port;
    }

    public void setPort(String port) {
        this.port = port;
    }

    public String getProtocol() {
        return protocol;
    }

    public void setProtocol(String protocol) {
        this.protocol = protocol;
    }

    public String getEnableSsl() {
        return enableSsl;
    }

    public void setEnableSsl(String enableSsl) {
        this.enableSsl = enableSsl;
    }

    public List<EmailAccount> getEmailList() {
        return emailList;
    }

    public void setEmailList(List<EmailAccount> emailList) {
        this.emailList = emailList;
    }
}
