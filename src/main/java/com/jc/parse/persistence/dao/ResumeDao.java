package com.jc.parse.persistence.dao;

import com.jc.base.config.db.annotation.AutoDataSource;
import com.jc.parse.bean.Resume;
import org.springframework.stereotype.Component;

/**
 * date : 2025-03-03 15:07:22
 * description : 爬虫简历(SpiderResume)表数据库访问层
 *
 * <AUTHOR>
 */
@Component
@AutoDataSource
public interface ResumeDao {

    /**
     * 新增数据
     *
     * @param resume 实例对象
     * @return 影响行数
     */
    int insertResume(Resume resume);



}

