package com.jc.parse.persistence.feign;

import com.alibaba.fastjson2.JSONObject;
import com.jc.base.bean.ResponseJson;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;

/**
 * date : 2024/10/22
 * description : 账号服务
 *
 * <AUTHOR> zhencai.cheng
 */
@FeignClient(name = "spider-account")
public interface AccountFeign {


    @PostMapping("/inner/spider/email/group")
    ResponseJson<JSONObject> getTaskEmailList(JSONObject queryMap);


    @PostMapping("/data/spider/email")
    ResponseJson<JSONObject> findEmail(JSONObject queryMap);

    /**
     * 根据邮箱获取账号信息
     *
     * @param queryMap 邮箱
     * @return 账号信息
     */
    @PostMapping("/inner/account/getByEmail.json")
    ResponseJson<JSONObject> getAccount(JSONObject queryMap);


}
