package com.jc.parse.persistence.feign.support;

import com.alibaba.fastjson2.JSONObject;
import com.jc.base.kits.MapKit;
import com.jc.parse.persistence.feign.MessageFeign;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

/**
 * date : 2025/07/17
 * description :
 *
 * <AUTHOR> czs
 */
@Component
public class MessageSupport {

    @Resource
    private MessageFeign messageFeign;

    /**
     * 添加弹窗消息
     *
     * @param message 消息
     */
    public void addPopupMessage(JSONObject message) {
        this.messageFeign.addPopupMessage(message);
    }

    /**
     * 添加弹窗消息
     *
     * @param receiverId  接收人
     * @param title       标题
     * @param content     内容
     * @param messageType 消息类型
     */
    public void addPopupMessage(Long receiverId, String title, String content, Integer messageType) {
        this.addPopupMessage(
                MapKit.of(
                        "receiverId", receiverId,
                        "title", title,
                        "content", content,
                        "messageType", messageType
                )
        );
    }

}
