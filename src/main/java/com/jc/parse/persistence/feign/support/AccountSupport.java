package com.jc.parse.persistence.feign.support;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.jc.base.bean.BaseAccount;
import com.jc.base.bean.ResponseJson;
import com.jc.base.exception.BusinessException;
import com.jc.base.kits.CollectionKit;
import com.jc.base.kits.MapKit;
import com.jc.base.persistence.redis.RedisCommonCache;
import com.jc.parse.bean.bo.EmailProtocol;
import com.jc.parse.constants.Constant;
import com.jc.parse.persistence.feign.AccountFeign;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.Set;

/**
 * date : 2024/10/22
 * description :
 *
 * <AUTHOR> zhencai.cheng
 */
@Component
public class AccountSupport {

    @Resource
    private AccountFeign accountFeign;
    @Resource
    private RedisCommonCache redisCommonCache;

    private final Logger logger = LoggerFactory.getLogger(AccountSupport.class);

    /**
     * 获取邮箱账号
     *
     * @return 采集邮箱集合
     */
    public List<EmailProtocol> getEmailList(JSONObject queryMap) {
        //先走缓存，没有走接口请求
        if (redisCommonCache.hasKey(Constant.Cache.COLLECT_EMAIL)) {
            Set<String> set = redisCommonCache.members(Constant.Cache.COLLECT_EMAIL);
            if (CollectionKit.isNotEmpty(set)) {
                return set.stream().map(item -> JSON.parseObject(item, EmailProtocol.class)).toList();
            }
        }
        ResponseJson<JSONObject> response = accountFeign.getTaskEmailList(queryMap);
        if (Objects.equals(response.getCode(), Constant.OK)) {
            JSONObject data = response.getData();
            if (CollectionKit.isNotEmpty(data)) {
                return data.getList(Constant.ROWS, EmailProtocol.class);
            }
        }
        logger.error("获取邮箱账号失败{}", response.getMsg());
        return CollectionKit.ofList();
    }

    public BaseAccount getAccount(String email) throws BusinessException {
        ResponseJson<JSONObject> response = accountFeign.getAccount(MapKit.of("email", email));
        if (Objects.equals(response.getCode(), Constant.OK)) {
            JSONObject data = response.getData();
            BaseAccount account = new BaseAccount();
            account.setAccountId(data.getLong("accountId"));
            account.setRealName(data.getString("realName"));
            account.setMobile(data.getString("mobile"));
            account.setAccount(data.getString("account"));
            return account;
        }
        throw new BusinessException("获取账号信息失败");
    }


    public JSONObject getEmail(String email) throws BusinessException {
        ResponseJson<JSONObject> response = accountFeign.findEmail(MapKit.of("email", email));
        if (Objects.equals(response.getCode(), Constant.OK)) {
            return response.getData();
        }
        throw new BusinessException("获取邮箱信息失败");
    }
}
