package com.jc.parse.persistence.feign;

import com.alibaba.fastjson2.JSONObject;
import com.jc.base.bean.ResponseJson;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;

/**
 * date : 2025/07/17
 * description : 消息服务
 *
 * <AUTHOR> czs
 */
@FeignClient(name = "jc-message")
public interface MessageFeign {

    /**
     * 添加弹窗消息
     *
     * @param message 消息
     * @return com.jc.base.bean.ResponseJson<com.alibaba.fastjson2.JSONObject>
     */
    @PostMapping("/inner/popup/message/add")
    Response<PERSON>son<JSONObject> addPopupMessage(JSONObject message);

}
