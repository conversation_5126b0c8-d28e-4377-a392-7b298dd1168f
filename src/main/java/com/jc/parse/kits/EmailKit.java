package com.jc.parse.kits;

import com.jc.base.kits.CollectionKit;
import com.jc.parse.bean.Resume;
import com.jc.parse.bean.bo.EmailContent;
import com.jc.parse.constants.Channel;
import com.jc.parse.service.resolver.Resolver;
import com.jc.parse.service.resolver.ResolverFactory;
import com.jc.parse.service.resolver.email.ChannelResolver;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.mail.*;
import javax.mail.internet.MimeMultipart;
import javax.mail.internet.MimeUtility;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.List;

/**
 * date : 2024/11/7
 * description :
 *
 * <AUTHOR> zhencai.cheng
 */
public class EmailKit {

    private static final Logger logger = LoggerFactory.getLogger(EmailKit.class);


    /**
     * 解析邮件
     *
     * @param message 消息
     * @param resume  简历
     * @throws Exception 异常
     */
    public static void parseMessage(Message message, Resume resume, Channel channel, String path) throws Exception {
        logger.info("邮件主题：" + message.getSubject());
        resume.setSubject(message.getSubject());
        resume.setResumeSource(channel.getValue());
        if (message.getContentType().contains("multipart")) {
            MimeMultipart multipart = (MimeMultipart) message.getContent();
            if (channel.getText()) {
                //获取邮件文本
                getEmailContent(message, multipart, channel, resume);
                logger.info("{}邮件内容解析：{}", channel.getDisplay(), resume);
            }
            for (int i = 0; i < multipart.getCount(); i++) {
                //附件处理
                BodyPart bodyPart = multipart.getBodyPart(i);
                getEmailAttachment(bodyPart, resume, path);
                logger.info("{}邮件文件解析：{}", channel.getDisplay(), resume);
            }
        } else if (message.getContentType().contains("text/html")) {
            getEmailHtml(message, channel, resume);
        }
    }

    /**
     * 获取渠道
     *
     * @param message 消息
     * @return 渠道
     * @throws MessagingException 消息异常
     */
    public static Channel getChannel(Message message) throws MessagingException {
        Address[] addresses = message.getFrom();
        for (Address address : addresses) {
            if (address.toString().contains(Channel.GAN_JI.getEmailSuffix())) {
                return Channel.GAN_JI;
            } else if (address.toString().contains(Channel.ZHAO_PIN.getEmailSuffix())) {
                return Channel.ZHAO_PIN;
            } else if (address.toString().contains(Channel.BOSS.getEmailSuffix())) {
                return Channel.BOSS;
            }
        }
        return Channel.UNKNOWN;
    }

    /**
     * 获取邮件内容
     *
     * @param message   message 消息
     * @param multipart multipart multipart
     * @param channel   channel 渠道
     * @param resume    resume 简历
     * @throws Exception 异常
     */
    private static void getEmailContent(Message message,
                                        MimeMultipart multipart,
                                        Channel channel,
                                        Resume resume) throws Exception {
        EmailContent content = new EmailContent();
        //获取邮件文本
        content.setSubject(message.getSubject());
        getTextFromMimeMultipart(multipart, content);
        //根据渠道解析
        ChannelResolver resolver = ResolverFactory.getEmailResolver(channel);
        //解析邮件文本
        resolver.parse(content, resume);
    }


    /**
     * 获取邮件附件
     *
     * @param bodyPart bodyPart
     * @param resume   resume
     * @return boolean
     * @throws Exception 异常
     */
    private static void getEmailAttachment(BodyPart bodyPart, Resume resume, String path) throws Exception {
        if (Part.ATTACHMENT.equalsIgnoreCase(bodyPart.getDisposition())) {
            //附件处理
            File file = saveAttachment(bodyPart, path);
            resume.setFileName(file.getName());
            //根据文件后缀名，获取解析器
            String key = file.getName().substring(
                    file.getName().lastIndexOf(".") + 1);
            try {
                if (ATTACHMENT_SUFFIX.contains(key)){
                    key = "image";
                }
                Resolver resolver = ResolverFactory.getParseService(key);
                //解析附件
                resolver.resolve(file, resume);
            } catch (Exception e) {
                logger.error("{}解析异常", file.getName(), e);
            }
        }
    }

    private static final List<String> ATTACHMENT_SUFFIX = CollectionKit.ofList("jpg", "jpeg", "png");

    /**
     * 附件保存
     *
     * @param bodyPart bodyPart
     * @throws IOException        io异常
     * @throws MessagingException 消息异常
     */
    private static File saveAttachment(BodyPart bodyPart, String path) throws IOException, MessagingException {
        // 附件名称,MimeUtility.decodeText避免编码异常
        String fileName = MimeUtility.decodeText(bodyPart.getFileName());
        InputStream is = bodyPart.getInputStream();
        File file = new File(path + fileName.replaceAll("[\\\\/:*?\"<>|]", "_"));
        FileOutputStream fos = null;
        try {
            fos = new FileOutputStream(file);
            byte[] buffer = new byte[1024];
            int len;
            while ((len = is.read(buffer)) != -1) {
                fos.write(buffer, 0, len);
            }
        } finally {
            is.close();
            if (fos != null) {
                fos.close();
            }
        }
        return file;
    }


    /**
     * 获取邮件文本内容
     *
     * @param mimeMultipart mimeMultipart
     * @throws MessagingException 消息异常
     * @throws IOException        io异常
     */
    private static void getTextFromMimeMultipart(MimeMultipart mimeMultipart, EmailContent content) throws MessagingException,
            IOException {
        int count = mimeMultipart.getCount();
        for (int i = 0; i < count; i++) {
            BodyPart bodyPart = mimeMultipart.getBodyPart(i);
            if (bodyPart.isMimeType("text/plain")) {
                content.setContent(bodyPart.getContent().toString());
            } else if (bodyPart.isMimeType("text/html")) {
                content.setHtml(bodyPart.getContent().toString());
            } else if (bodyPart.isMimeType("multipart/*")) {
                getTextFromMimeMultipart((MimeMultipart) bodyPart.getContent(), content);
            }
        }
    }

    /**
     * 获取邮件html内容
     *
     * @param message message
     * @param channel channel
     * @param resume  resume
     * @throws Exception 异常
     */
    private static void getEmailHtml(Message message, Channel channel, Resume resume) throws Exception {
        EmailContent content = new EmailContent();
        resume.setFileName(message.getSubject());
        content.setHtml(message.getContent().toString());
        ChannelResolver resolver = ResolverFactory.getEmailResolver(channel);
        resolver.parse(content, resume);
    }
}
