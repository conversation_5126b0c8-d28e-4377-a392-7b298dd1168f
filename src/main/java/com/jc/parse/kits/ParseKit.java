package com.jc.parse.kits;

import com.jc.base.kits.CollectionKit;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * date : 2024/11/6
 * description :
 *
 * <AUTHOR> zhencai.cheng
 */
public class ParseKit {


    /**
     * 匹配
     *
     * @param content 文本内容
     * @param pattern 正则
     * @param index   匹配位置
     * @return 匹配结果
     */
    public static String match(String content, Pattern pattern, int... index) {
        var i = 0;
        if (!CollectionKit.isEmpty(index)) {
            i = index[0];
        }
        Matcher matcher = pattern.matcher(content);
        if (matcher.find()) {
            if (matcher.groupCount() >= i) {
                return matcher.group(i);
            } else {
                return matcher.group();
            }
        }
        return null;
    }
}
